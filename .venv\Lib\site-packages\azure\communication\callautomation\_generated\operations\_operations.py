# pylint: disable=too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
from collections.abc import MutableMapping
import datetime
from io import IOBase
from typing import Any, Callable, Dict, IO, Optional, TypeVar, Union, overload
import urllib.parse
import uuid

from azure.core import PipelineClient
from azure.core.exceptions import (
    ClientAuthenticationError,
    HttpResponseError,
    ResourceExists<PERSON>rror,
    ResourceNotFoundError,
    ResourceNotModifiedError,
    map_error,
)
from azure.core.paging import ItemPaged
from azure.core.pipeline import PipelineResponse
from azure.core.rest import HttpRequest, HttpResponse
from azure.core.tracing.decorator import distributed_trace
from azure.core.utils import case_insensitive_dict

from .. import models as _models
from .._configuration import AzureCommunicationCallAutomationServiceConfiguration
from .._utils.serialization import Deserializer, Serializer
from .._utils.utils import ClientMixinABC

T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, HttpResponse], T, Dict[str, Any]], Any]]

_SERIALIZER = Serializer()
_SERIALIZER.client_side_validation = False


def build_azure_communication_call_automation_service_create_call_request(  # pylint: disable=name-too-long
    **kwargs: Any,
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections"

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_azure_communication_call_automation_service_answer_call_request(  # pylint: disable=name-too-long
    **kwargs: Any,
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections:answer"

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_azure_communication_call_automation_service_redirect_call_request(  # pylint: disable=name-too-long
    **kwargs: Any,
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections:redirect"

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_azure_communication_call_automation_service_reject_call_request(  # pylint: disable=name-too-long
    **kwargs: Any,
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections:reject"

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_azure_communication_call_automation_service_connect_request(  # pylint: disable=name-too-long
    **kwargs: Any,
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections:connect"

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_get_call_request(call_connection_id: str, **kwargs: Any) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_hangup_call_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="DELETE", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_terminate_call_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:terminate"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_transfer_to_participant_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:transferToParticipant"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_get_participants_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}/participants"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_add_participant_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}/participants:add"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_remove_participant_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}/participants:remove"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_mute_request(call_connection_id: str, **kwargs: Any) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}/participants:mute"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_cancel_add_participant_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}/participants:cancelAddParticipant"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_connection_get_participant_request(  # pylint: disable=name-too-long
    call_connection_id: str, participant_raw_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}/participants/{participantRawId}"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
        "participantRawId": _SERIALIZER.url("participant_raw_id", participant_raw_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_play_request(call_connection_id: str, **kwargs: Any) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:play"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_start_transcription_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:startTranscription"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_stop_transcription_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:stopTranscription"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_update_transcription_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:updateTranscription"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_cancel_all_media_operations_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:cancelAllMediaOperations"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_recognize_request(call_connection_id: str, **kwargs: Any) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:recognize"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_start_continuous_dtmf_recognition_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:startContinuousDtmfRecognition"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_stop_continuous_dtmf_recognition_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:stopContinuousDtmfRecognition"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_send_dtmf_tones_request(call_connection_id: str, **kwargs: Any) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:sendDtmfTones"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_hold_request(call_connection_id: str, **kwargs: Any) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:hold"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_unhold_request(call_connection_id: str, **kwargs: Any) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:unhold"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_start_media_streaming_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:startMediaStreaming"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_media_stop_media_streaming_request(  # pylint: disable=name-too-long
    call_connection_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/callConnections/{callConnectionId}:stopMediaStreaming"
    path_format_arguments = {
        "callConnectionId": _SERIALIZER.url("call_connection_id", call_connection_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_recording_start_recording_request(**kwargs: Any) -> HttpRequest:  # pylint: disable=name-too-long
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/recordings"

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    if "Repeatability-Request-ID" not in _headers:
        _headers["Repeatability-Request-ID"] = str(uuid.uuid4())
    if "Repeatability-First-Sent" not in _headers:
        _headers["Repeatability-First-Sent"] = _SERIALIZER.serialize_data(
            datetime.datetime.now(datetime.timezone.utc), "rfc-1123"
        )
    if content_type is not None:
        _headers["Content-Type"] = _SERIALIZER.header("content_type", content_type, "str")
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_recording_get_recording_properties_request(  # pylint: disable=name-too-long
    recording_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/recordings/{recordingId}"
    path_format_arguments = {
        "recordingId": _SERIALIZER.url("recording_id", recording_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="GET", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_recording_stop_recording_request(  # pylint: disable=name-too-long
    recording_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/recordings/{recordingId}"
    path_format_arguments = {
        "recordingId": _SERIALIZER.url("recording_id", recording_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="DELETE", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_recording_pause_recording_request(  # pylint: disable=name-too-long
    recording_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/recordings/{recordingId}:pause"
    path_format_arguments = {
        "recordingId": _SERIALIZER.url("recording_id", recording_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


def build_call_recording_resume_recording_request(  # pylint: disable=name-too-long
    recording_id: str, **kwargs: Any
) -> HttpRequest:
    _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
    _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

    api_version: str = kwargs.pop("api_version", _params.pop("api-version", "2025-05-15"))
    accept = _headers.pop("Accept", "application/json")

    # Construct URL
    _url = "/calling/recordings/{recordingId}:resume"
    path_format_arguments = {
        "recordingId": _SERIALIZER.url("recording_id", recording_id, "str"),
    }

    _url: str = _url.format(**path_format_arguments)  # type: ignore

    # Construct parameters
    _params["api-version"] = _SERIALIZER.query("api_version", api_version, "str")

    # Construct headers
    _headers["Accept"] = _SERIALIZER.header("accept", accept, "str")

    return HttpRequest(method="POST", url=_url, params=_params, headers=_headers, **kwargs)


class AzureCommunicationCallAutomationServiceOperationsMixin(  # pylint: disable=name-too-long
    ClientMixinABC[PipelineClient[HttpRequest, HttpResponse], AzureCommunicationCallAutomationServiceConfiguration]
):

    @overload
    def create_call(
        self, create_call_request: _models.CreateCallRequest, *, content_type: str = "application/json", **kwargs: Any
    ) -> _models.CallConnectionProperties:
        """Create an outbound call.

        Create an outbound call.

        :param create_call_request: The create call request. Required.
        :type create_call_request: ~azure.communication.callautomation.models.CreateCallRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def create_call(
        self, create_call_request: IO[bytes], *, content_type: str = "application/json", **kwargs: Any
    ) -> _models.CallConnectionProperties:
        """Create an outbound call.

        Create an outbound call.

        :param create_call_request: The create call request. Required.
        :type create_call_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def create_call(
        self, create_call_request: Union[_models.CreateCallRequest, IO[bytes]], **kwargs: Any
    ) -> _models.CallConnectionProperties:
        """Create an outbound call.

        Create an outbound call.

        :param create_call_request: The create call request. Is either a CreateCallRequest type or a
         IO[bytes] type. Required.
        :type create_call_request: ~azure.communication.callautomation.models.CreateCallRequest or
         IO[bytes]
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.CallConnectionProperties] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(create_call_request, (IOBase, bytes)):
            _content = create_call_request
        else:
            _json = self._serialize.body(create_call_request, "CreateCallRequest")

        _request = build_azure_communication_call_automation_service_create_call_request(
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("CallConnectionProperties", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @overload
    def answer_call(
        self, answer_call_request: _models.AnswerCallRequest, *, content_type: str = "application/json", **kwargs: Any
    ) -> _models.CallConnectionProperties:
        """Answer a Call.

        Answer a call using the IncomingCallContext from Event Grid.

        :param answer_call_request: The answer call request. Required.
        :type answer_call_request: ~azure.communication.callautomation.models.AnswerCallRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def answer_call(
        self, answer_call_request: IO[bytes], *, content_type: str = "application/json", **kwargs: Any
    ) -> _models.CallConnectionProperties:
        """Answer a Call.

        Answer a call using the IncomingCallContext from Event Grid.

        :param answer_call_request: The answer call request. Required.
        :type answer_call_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def answer_call(
        self, answer_call_request: Union[_models.AnswerCallRequest, IO[bytes]], **kwargs: Any
    ) -> _models.CallConnectionProperties:
        """Answer a Call.

        Answer a call using the IncomingCallContext from Event Grid.

        :param answer_call_request: The answer call request. Is either a AnswerCallRequest type or a
         IO[bytes] type. Required.
        :type answer_call_request: ~azure.communication.callautomation.models.AnswerCallRequest or
         IO[bytes]
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.CallConnectionProperties] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(answer_call_request, (IOBase, bytes)):
            _content = answer_call_request
        else:
            _json = self._serialize.body(answer_call_request, "AnswerCallRequest")

        _request = build_azure_communication_call_automation_service_answer_call_request(
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("CallConnectionProperties", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @overload
    def redirect_call(
        self,
        redirect_call_request: _models.RedirectCallRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Redirect a call.

        Redirect a call.

        :param redirect_call_request: The redirect call request. Required.
        :type redirect_call_request: ~azure.communication.callautomation.models.RedirectCallRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def redirect_call(
        self, redirect_call_request: IO[bytes], *, content_type: str = "application/json", **kwargs: Any
    ) -> None:
        """Redirect a call.

        Redirect a call.

        :param redirect_call_request: The redirect call request. Required.
        :type redirect_call_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def redirect_call(  # pylint: disable=inconsistent-return-statements
        self, redirect_call_request: Union[_models.RedirectCallRequest, IO[bytes]], **kwargs: Any
    ) -> None:
        """Redirect a call.

        Redirect a call.

        :param redirect_call_request: The redirect call request. Is either a RedirectCallRequest type
         or a IO[bytes] type. Required.
        :type redirect_call_request: ~azure.communication.callautomation.models.RedirectCallRequest or
         IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(redirect_call_request, (IOBase, bytes)):
            _content = redirect_call_request
        else:
            _json = self._serialize.body(redirect_call_request, "RedirectCallRequest")

        _request = build_azure_communication_call_automation_service_redirect_call_request(
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def reject_call(
        self, reject_call_request: _models.RejectCallRequest, *, content_type: str = "application/json", **kwargs: Any
    ) -> None:
        """Reject the call.

        Reject the call.

        :param reject_call_request: The reject call request. Required.
        :type reject_call_request: ~azure.communication.callautomation.models.RejectCallRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def reject_call(
        self, reject_call_request: IO[bytes], *, content_type: str = "application/json", **kwargs: Any
    ) -> None:
        """Reject the call.

        Reject the call.

        :param reject_call_request: The reject call request. Required.
        :type reject_call_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def reject_call(  # pylint: disable=inconsistent-return-statements
        self, reject_call_request: Union[_models.RejectCallRequest, IO[bytes]], **kwargs: Any
    ) -> None:
        """Reject the call.

        Reject the call.

        :param reject_call_request: The reject call request. Is either a RejectCallRequest type or a
         IO[bytes] type. Required.
        :type reject_call_request: ~azure.communication.callautomation.models.RejectCallRequest or
         IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(reject_call_request, (IOBase, bytes)):
            _content = reject_call_request
        else:
            _json = self._serialize.body(reject_call_request, "RejectCallRequest")

        _request = build_azure_communication_call_automation_service_reject_call_request(
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def connect(
        self, connect_request: _models.ConnectRequest, *, content_type: str = "application/json", **kwargs: Any
    ) -> _models.CallConnectionProperties:
        """Create a Connection to a CallLocator.

        Create a connection to a CallLocator.

        :param connect_request: The create connection request. Required.
        :type connect_request: ~azure.communication.callautomation.models.ConnectRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def connect(
        self, connect_request: IO[bytes], *, content_type: str = "application/json", **kwargs: Any
    ) -> _models.CallConnectionProperties:
        """Create a Connection to a CallLocator.

        Create a connection to a CallLocator.

        :param connect_request: The create connection request. Required.
        :type connect_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def connect(
        self, connect_request: Union[_models.ConnectRequest, IO[bytes]], **kwargs: Any
    ) -> _models.CallConnectionProperties:
        """Create a Connection to a CallLocator.

        Create a connection to a CallLocator.

        :param connect_request: The create connection request. Is either a ConnectRequest type or a
         IO[bytes] type. Required.
        :type connect_request: ~azure.communication.callautomation.models.ConnectRequest or IO[bytes]
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.CallConnectionProperties] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(connect_request, (IOBase, bytes)):
            _content = connect_request
        else:
            _json = self._serialize.body(connect_request, "ConnectRequest")

        _request = build_azure_communication_call_automation_service_connect_request(
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("CallConnectionProperties", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore


class CallConnectionOperations:
    """
    .. warning::
        **DO NOT** instantiate this class directly.

        Instead, you should access the following operations through
        :class:`~azure.communication.callautomation.AzureCommunicationCallAutomationService`'s
        :attr:`call_connection` attribute.
    """

    models = _models

    def __init__(self, *args, **kwargs) -> None:
        input_args = list(args)
        self._client: PipelineClient = input_args.pop(0) if input_args else kwargs.pop("client")
        self._config: AzureCommunicationCallAutomationServiceConfiguration = (
            input_args.pop(0) if input_args else kwargs.pop("config")
        )
        self._serialize: Serializer = input_args.pop(0) if input_args else kwargs.pop("serializer")
        self._deserialize: Deserializer = input_args.pop(0) if input_args else kwargs.pop("deserializer")

    @distributed_trace
    def get_call(self, call_connection_id: str, **kwargs: Any) -> _models.CallConnectionProperties:
        """Get the detail properties of an ongoing call.

        Get the detail properties of an ongoing call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :return: CallConnectionProperties
        :rtype: ~azure.communication.callautomation.models.CallConnectionProperties
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[_models.CallConnectionProperties] = kwargs.pop("cls", None)

        _request = build_call_connection_get_call_request(
            call_connection_id=call_connection_id,
            api_version=self._config.api_version,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("CallConnectionProperties", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def hangup_call(  # pylint: disable=inconsistent-return-statements
        self, call_connection_id: str, **kwargs: Any
    ) -> None:
        """Hang up call automation service from the call. This will make call automation service leave the
        call, but does not terminate if there are more than 1 caller in the call.

        Hang up call automation service from the call. This will make call automation service leave the
        call, but does not terminate if there are more than 1 caller in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_call_connection_hangup_call_request(
            call_connection_id=call_connection_id,
            api_version=self._config.api_version,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @distributed_trace
    def terminate_call(  # pylint: disable=inconsistent-return-statements
        self, call_connection_id: str, **kwargs: Any
    ) -> None:
        """Terminate a call using CallConnectionId.

        Terminate a call using CallConnectionId.

        :param call_connection_id: The terminate call request. Required.
        :type call_connection_id: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_call_connection_terminate_call_request(
            call_connection_id=call_connection_id,
            api_version=self._config.api_version,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def transfer_to_participant(
        self,
        call_connection_id: str,
        transfer_to_participant_request: _models.TransferToParticipantRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.TransferCallResponse:
        """Transfer the call to a participant.

        Transfer the call to a participant.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param transfer_to_participant_request: The transfer to participant request. Required.
        :type transfer_to_participant_request:
         ~azure.communication.callautomation.models.TransferToParticipantRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: TransferCallResponse
        :rtype: ~azure.communication.callautomation.models.TransferCallResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def transfer_to_participant(
        self,
        call_connection_id: str,
        transfer_to_participant_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.TransferCallResponse:
        """Transfer the call to a participant.

        Transfer the call to a participant.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param transfer_to_participant_request: The transfer to participant request. Required.
        :type transfer_to_participant_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: TransferCallResponse
        :rtype: ~azure.communication.callautomation.models.TransferCallResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def transfer_to_participant(
        self,
        call_connection_id: str,
        transfer_to_participant_request: Union[_models.TransferToParticipantRequest, IO[bytes]],
        **kwargs: Any,
    ) -> _models.TransferCallResponse:
        """Transfer the call to a participant.

        Transfer the call to a participant.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param transfer_to_participant_request: The transfer to participant request. Is either a
         TransferToParticipantRequest type or a IO[bytes] type. Required.
        :type transfer_to_participant_request:
         ~azure.communication.callautomation.models.TransferToParticipantRequest or IO[bytes]
        :return: TransferCallResponse
        :rtype: ~azure.communication.callautomation.models.TransferCallResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.TransferCallResponse] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(transfer_to_participant_request, (IOBase, bytes)):
            _content = transfer_to_participant_request
        else:
            _json = self._serialize.body(transfer_to_participant_request, "TransferToParticipantRequest")

        _request = build_call_connection_transfer_to_participant_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("TransferCallResponse", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def get_participants(self, call_connection_id: str, **kwargs: Any) -> ItemPaged["_models.CallParticipant"]:
        """Get participants from a call. Recording and transcription bots are omitted from this list.

        Get participants from a call. Recording and transcription bots are omitted from this list.

        :param call_connection_id: The call connection Id. Required.
        :type call_connection_id: str
        :return: An iterator like instance of CallParticipant
        :rtype:
         ~azure.core.paging.ItemPaged[~azure.communication.callautomation.models.CallParticipant]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[_models._models.GetParticipantsResponse] = kwargs.pop("cls", None)

        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        def prepare_request(next_link=None):
            if not next_link:

                _request = build_call_connection_get_participants_request(
                    call_connection_id=call_connection_id,
                    api_version=self._config.api_version,
                    headers=_headers,
                    params=_params,
                )
                path_format_arguments = {
                    "endpoint": self._serialize.url(
                        "self._config.endpoint", self._config.endpoint, "str", skip_quote=True
                    ),
                }
                _request.url = self._client.format_url(_request.url, **path_format_arguments)

            else:
                # make call to next link with the client's api-version
                _parsed_next_link = urllib.parse.urlparse(next_link)
                _next_request_params = case_insensitive_dict(
                    {
                        key: [urllib.parse.quote(v) for v in value]
                        for key, value in urllib.parse.parse_qs(_parsed_next_link.query).items()
                    }
                )
                _next_request_params["api-version"] = self._config.api_version
                _request = HttpRequest(
                    "GET", urllib.parse.urljoin(next_link, _parsed_next_link.path), params=_next_request_params
                )
                path_format_arguments = {
                    "endpoint": self._serialize.url(
                        "self._config.endpoint", self._config.endpoint, "str", skip_quote=True
                    ),
                }
                _request.url = self._client.format_url(_request.url, **path_format_arguments)

            return _request

        def extract_data(pipeline_response):
            deserialized = self._deserialize(
                _models._models.GetParticipantsResponse, pipeline_response  # pylint: disable=protected-access
            )
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)  # type: ignore
            return deserialized.next_link or None, iter(list_of_elem)

        def get_next(next_link=None):
            _request = prepare_request(next_link)

            _stream = False
            pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
                _request, stream=_stream, **kwargs
            )
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
                raise HttpResponseError(response=response, model=error)

            return pipeline_response

        return ItemPaged(get_next, extract_data)

    @overload
    def add_participant(
        self,
        call_connection_id: str,
        add_participant_request: _models.AddParticipantRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.AddParticipantResponse:
        """Add a participant to the call.

        Add a participant to the call.

        :param call_connection_id: The call connection Id. Required.
        :type call_connection_id: str
        :param add_participant_request: Required.
        :type add_participant_request: ~azure.communication.callautomation.models.AddParticipantRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: AddParticipantResponse
        :rtype: ~azure.communication.callautomation.models.AddParticipantResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def add_participant(
        self,
        call_connection_id: str,
        add_participant_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.AddParticipantResponse:
        """Add a participant to the call.

        Add a participant to the call.

        :param call_connection_id: The call connection Id. Required.
        :type call_connection_id: str
        :param add_participant_request: Required.
        :type add_participant_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: AddParticipantResponse
        :rtype: ~azure.communication.callautomation.models.AddParticipantResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def add_participant(
        self,
        call_connection_id: str,
        add_participant_request: Union[_models.AddParticipantRequest, IO[bytes]],
        **kwargs: Any,
    ) -> _models.AddParticipantResponse:
        """Add a participant to the call.

        Add a participant to the call.

        :param call_connection_id: The call connection Id. Required.
        :type call_connection_id: str
        :param add_participant_request: Is either a AddParticipantRequest type or a IO[bytes] type.
         Required.
        :type add_participant_request: ~azure.communication.callautomation.models.AddParticipantRequest
         or IO[bytes]
        :return: AddParticipantResponse
        :rtype: ~azure.communication.callautomation.models.AddParticipantResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.AddParticipantResponse] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(add_participant_request, (IOBase, bytes)):
            _content = add_participant_request
        else:
            _json = self._serialize.body(add_participant_request, "AddParticipantRequest")

        _request = build_call_connection_add_participant_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("AddParticipantResponse", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @overload
    def remove_participant(
        self,
        call_connection_id: str,
        remove_participant_request: _models.RemoveParticipantRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.RemoveParticipantResponse:
        """Remove a participant from the call using identifier.

        Remove a participant from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param remove_participant_request: The participant to be removed from the call. Required.
        :type remove_participant_request:
         ~azure.communication.callautomation.models.RemoveParticipantRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: RemoveParticipantResponse
        :rtype: ~azure.communication.callautomation.models.RemoveParticipantResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def remove_participant(
        self,
        call_connection_id: str,
        remove_participant_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.RemoveParticipantResponse:
        """Remove a participant from the call using identifier.

        Remove a participant from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param remove_participant_request: The participant to be removed from the call. Required.
        :type remove_participant_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: RemoveParticipantResponse
        :rtype: ~azure.communication.callautomation.models.RemoveParticipantResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def remove_participant(
        self,
        call_connection_id: str,
        remove_participant_request: Union[_models.RemoveParticipantRequest, IO[bytes]],
        **kwargs: Any,
    ) -> _models.RemoveParticipantResponse:
        """Remove a participant from the call using identifier.

        Remove a participant from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param remove_participant_request: The participant to be removed from the call. Is either a
         RemoveParticipantRequest type or a IO[bytes] type. Required.
        :type remove_participant_request:
         ~azure.communication.callautomation.models.RemoveParticipantRequest or IO[bytes]
        :return: RemoveParticipantResponse
        :rtype: ~azure.communication.callautomation.models.RemoveParticipantResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.RemoveParticipantResponse] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(remove_participant_request, (IOBase, bytes)):
            _content = remove_participant_request
        else:
            _json = self._serialize.body(remove_participant_request, "RemoveParticipantRequest")

        _request = build_call_connection_remove_participant_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("RemoveParticipantResponse", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @overload
    def mute(
        self,
        call_connection_id: str,
        mute_participants_request: _models.MuteParticipantsRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.MuteParticipantsResult:
        """Mute participants from the call using identifier.

        Mute participants from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param mute_participants_request: The participants to be muted from the call. Required.
        :type mute_participants_request:
         ~azure.communication.callautomation.models.MuteParticipantsRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: MuteParticipantsResult
        :rtype: ~azure.communication.callautomation.models.MuteParticipantsResult
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def mute(
        self,
        call_connection_id: str,
        mute_participants_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.MuteParticipantsResult:
        """Mute participants from the call using identifier.

        Mute participants from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param mute_participants_request: The participants to be muted from the call. Required.
        :type mute_participants_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: MuteParticipantsResult
        :rtype: ~azure.communication.callautomation.models.MuteParticipantsResult
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def mute(
        self,
        call_connection_id: str,
        mute_participants_request: Union[_models.MuteParticipantsRequest, IO[bytes]],
        **kwargs: Any,
    ) -> _models.MuteParticipantsResult:
        """Mute participants from the call using identifier.

        Mute participants from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param mute_participants_request: The participants to be muted from the call. Is either a
         MuteParticipantsRequest type or a IO[bytes] type. Required.
        :type mute_participants_request:
         ~azure.communication.callautomation.models.MuteParticipantsRequest or IO[bytes]
        :return: MuteParticipantsResult
        :rtype: ~azure.communication.callautomation.models.MuteParticipantsResult
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.MuteParticipantsResult] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(mute_participants_request, (IOBase, bytes)):
            _content = mute_participants_request
        else:
            _json = self._serialize.body(mute_participants_request, "MuteParticipantsRequest")

        _request = build_call_connection_mute_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("MuteParticipantsResult", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @overload
    def cancel_add_participant(
        self,
        call_connection_id: str,
        cancel_add_participant_request: _models.CancelAddParticipantRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.CancelAddParticipantResponse:
        """Cancel add participant operation.

        Cancel add participant operation.

        :param call_connection_id: The call connection Id. Required.
        :type call_connection_id: str
        :param cancel_add_participant_request: Cancellation request. Required.
        :type cancel_add_participant_request:
         ~azure.communication.callautomation.models.CancelAddParticipantRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CancelAddParticipantResponse
        :rtype: ~azure.communication.callautomation.models.CancelAddParticipantResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def cancel_add_participant(
        self,
        call_connection_id: str,
        cancel_add_participant_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.CancelAddParticipantResponse:
        """Cancel add participant operation.

        Cancel add participant operation.

        :param call_connection_id: The call connection Id. Required.
        :type call_connection_id: str
        :param cancel_add_participant_request: Cancellation request. Required.
        :type cancel_add_participant_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: CancelAddParticipantResponse
        :rtype: ~azure.communication.callautomation.models.CancelAddParticipantResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def cancel_add_participant(
        self,
        call_connection_id: str,
        cancel_add_participant_request: Union[_models.CancelAddParticipantRequest, IO[bytes]],
        **kwargs: Any,
    ) -> _models.CancelAddParticipantResponse:
        """Cancel add participant operation.

        Cancel add participant operation.

        :param call_connection_id: The call connection Id. Required.
        :type call_connection_id: str
        :param cancel_add_participant_request: Cancellation request. Is either a
         CancelAddParticipantRequest type or a IO[bytes] type. Required.
        :type cancel_add_participant_request:
         ~azure.communication.callautomation.models.CancelAddParticipantRequest or IO[bytes]
        :return: CancelAddParticipantResponse
        :rtype: ~azure.communication.callautomation.models.CancelAddParticipantResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.CancelAddParticipantResponse] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(cancel_add_participant_request, (IOBase, bytes)):
            _content = cancel_add_participant_request
        else:
            _json = self._serialize.body(cancel_add_participant_request, "CancelAddParticipantRequest")

        _request = build_call_connection_cancel_add_participant_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("CancelAddParticipantResponse", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def get_participant(
        self, call_connection_id: str, participant_raw_id: str, **kwargs: Any
    ) -> _models.CallParticipant:
        """Get participant from a call.

        Get participant from a call.

        :param call_connection_id: The call connection Id. Required.
        :type call_connection_id: str
        :param participant_raw_id: Raw id of the participant to retrieve. Required.
        :type participant_raw_id: str
        :return: CallParticipant
        :rtype: ~azure.communication.callautomation.models.CallParticipant
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[_models.CallParticipant] = kwargs.pop("cls", None)

        _request = build_call_connection_get_participant_request(
            call_connection_id=call_connection_id,
            participant_raw_id=participant_raw_id,
            api_version=self._config.api_version,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("CallParticipant", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore


class CallMediaOperations:
    """
    .. warning::
        **DO NOT** instantiate this class directly.

        Instead, you should access the following operations through
        :class:`~azure.communication.callautomation.AzureCommunicationCallAutomationService`'s
        :attr:`call_media` attribute.
    """

    models = _models

    def __init__(self, *args, **kwargs) -> None:
        input_args = list(args)
        self._client: PipelineClient = input_args.pop(0) if input_args else kwargs.pop("client")
        self._config: AzureCommunicationCallAutomationServiceConfiguration = (
            input_args.pop(0) if input_args else kwargs.pop("config")
        )
        self._serialize: Serializer = input_args.pop(0) if input_args else kwargs.pop("serializer")
        self._deserialize: Deserializer = input_args.pop(0) if input_args else kwargs.pop("deserializer")

    @overload
    def play(
        self,
        call_connection_id: str,
        play_request: _models.PlayRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Plays audio to participants in the call.

        Plays audio to participants in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param play_request: play request payload. Required.
        :type play_request: ~azure.communication.callautomation.models.PlayRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def play(
        self, call_connection_id: str, play_request: IO[bytes], *, content_type: str = "application/json", **kwargs: Any
    ) -> None:
        """Plays audio to participants in the call.

        Plays audio to participants in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param play_request: play request payload. Required.
        :type play_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def play(  # pylint: disable=inconsistent-return-statements
        self, call_connection_id: str, play_request: Union[_models.PlayRequest, IO[bytes]], **kwargs: Any
    ) -> None:
        """Plays audio to participants in the call.

        Plays audio to participants in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param play_request: play request payload. Is either a PlayRequest type or a IO[bytes] type.
         Required.
        :type play_request: ~azure.communication.callautomation.models.PlayRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(play_request, (IOBase, bytes)):
            _content = play_request
        else:
            _json = self._serialize.body(play_request, "PlayRequest")

        _request = build_call_media_play_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def start_transcription(
        self,
        call_connection_id: str,
        start_transcription_request: _models.StartTranscriptionRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Starts transcription in the call.

        Starts transcription in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param start_transcription_request: Required.
        :type start_transcription_request:
         ~azure.communication.callautomation.models.StartTranscriptionRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def start_transcription(
        self,
        call_connection_id: str,
        start_transcription_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Starts transcription in the call.

        Starts transcription in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param start_transcription_request: Required.
        :type start_transcription_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def start_transcription(  # pylint: disable=inconsistent-return-statements
        self,
        call_connection_id: str,
        start_transcription_request: Union[_models.StartTranscriptionRequest, IO[bytes]],
        **kwargs: Any,
    ) -> None:
        """Starts transcription in the call.

        Starts transcription in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param start_transcription_request: Is either a StartTranscriptionRequest type or a IO[bytes]
         type. Required.
        :type start_transcription_request:
         ~azure.communication.callautomation.models.StartTranscriptionRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(start_transcription_request, (IOBase, bytes)):
            _content = start_transcription_request
        else:
            _json = self._serialize.body(start_transcription_request, "StartTranscriptionRequest")

        _request = build_call_media_start_transcription_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def stop_transcription(
        self,
        call_connection_id: str,
        stop_transcription_request: _models.StopTranscriptionRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Stops transcription in the call.

        Stops transcription in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param stop_transcription_request: stop transcription request payload. Required.
        :type stop_transcription_request:
         ~azure.communication.callautomation.models.StopTranscriptionRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def stop_transcription(
        self,
        call_connection_id: str,
        stop_transcription_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Stops transcription in the call.

        Stops transcription in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param stop_transcription_request: stop transcription request payload. Required.
        :type stop_transcription_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def stop_transcription(  # pylint: disable=inconsistent-return-statements
        self,
        call_connection_id: str,
        stop_transcription_request: Union[_models.StopTranscriptionRequest, IO[bytes]],
        **kwargs: Any,
    ) -> None:
        """Stops transcription in the call.

        Stops transcription in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param stop_transcription_request: stop transcription request payload. Is either a
         StopTranscriptionRequest type or a IO[bytes] type. Required.
        :type stop_transcription_request:
         ~azure.communication.callautomation.models.StopTranscriptionRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(stop_transcription_request, (IOBase, bytes)):
            _content = stop_transcription_request
        else:
            _json = self._serialize.body(stop_transcription_request, "StopTranscriptionRequest")

        _request = build_call_media_stop_transcription_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def update_transcription(
        self,
        call_connection_id: str,
        update_transcription_request: _models.UpdateTranscriptionRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """UpdateTranscription Api.

        API to change transcription language.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param update_transcription_request: The UpdateTranscription request. Required.
        :type update_transcription_request:
         ~azure.communication.callautomation.models.UpdateTranscriptionRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def update_transcription(
        self,
        call_connection_id: str,
        update_transcription_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """UpdateTranscription Api.

        API to change transcription language.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param update_transcription_request: The UpdateTranscription request. Required.
        :type update_transcription_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def update_transcription(  # pylint: disable=inconsistent-return-statements
        self,
        call_connection_id: str,
        update_transcription_request: Union[_models.UpdateTranscriptionRequest, IO[bytes]],
        **kwargs: Any,
    ) -> None:
        """UpdateTranscription Api.

        API to change transcription language.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param update_transcription_request: The UpdateTranscription request. Is either a
         UpdateTranscriptionRequest type or a IO[bytes] type. Required.
        :type update_transcription_request:
         ~azure.communication.callautomation.models.UpdateTranscriptionRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(update_transcription_request, (IOBase, bytes)):
            _content = update_transcription_request
        else:
            _json = self._serialize.body(update_transcription_request, "UpdateTranscriptionRequest")

        _request = build_call_media_update_transcription_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @distributed_trace
    def cancel_all_media_operations(  # pylint: disable=inconsistent-return-statements
        self, call_connection_id: str, **kwargs: Any
    ) -> None:
        """Cancel all media operations in a call.

        Cancel all media operations in a call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_call_media_cancel_all_media_operations_request(
            call_connection_id=call_connection_id,
            api_version=self._config.api_version,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def recognize(
        self,
        call_connection_id: str,
        recognize_request: _models.RecognizeRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Recognize media from call.

        Recognize media from call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param recognize_request: The media recognize request. Required.
        :type recognize_request: ~azure.communication.callautomation.models.RecognizeRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def recognize(
        self,
        call_connection_id: str,
        recognize_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Recognize media from call.

        Recognize media from call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param recognize_request: The media recognize request. Required.
        :type recognize_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def recognize(  # pylint: disable=inconsistent-return-statements
        self, call_connection_id: str, recognize_request: Union[_models.RecognizeRequest, IO[bytes]], **kwargs: Any
    ) -> None:
        """Recognize media from call.

        Recognize media from call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param recognize_request: The media recognize request. Is either a RecognizeRequest type or a
         IO[bytes] type. Required.
        :type recognize_request: ~azure.communication.callautomation.models.RecognizeRequest or
         IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(recognize_request, (IOBase, bytes)):
            _content = recognize_request
        else:
            _json = self._serialize.body(recognize_request, "RecognizeRequest")

        _request = build_call_media_recognize_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def start_continuous_dtmf_recognition(
        self,
        call_connection_id: str,
        continuous_dtmf_recognition_request: _models.ContinuousDtmfRecognitionRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Start continuous Dtmf recognition by subscribing to tones.

        Start continuous Dtmf recognition by subscribing to tones.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param continuous_dtmf_recognition_request: The continuous recognize request. Required.
        :type continuous_dtmf_recognition_request:
         ~azure.communication.callautomation.models.ContinuousDtmfRecognitionRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def start_continuous_dtmf_recognition(
        self,
        call_connection_id: str,
        continuous_dtmf_recognition_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Start continuous Dtmf recognition by subscribing to tones.

        Start continuous Dtmf recognition by subscribing to tones.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param continuous_dtmf_recognition_request: The continuous recognize request. Required.
        :type continuous_dtmf_recognition_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def start_continuous_dtmf_recognition(  # pylint: disable=inconsistent-return-statements
        self,
        call_connection_id: str,
        continuous_dtmf_recognition_request: Union[_models.ContinuousDtmfRecognitionRequest, IO[bytes]],
        **kwargs: Any,
    ) -> None:
        """Start continuous Dtmf recognition by subscribing to tones.

        Start continuous Dtmf recognition by subscribing to tones.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param continuous_dtmf_recognition_request: The continuous recognize request. Is either a
         ContinuousDtmfRecognitionRequest type or a IO[bytes] type. Required.
        :type continuous_dtmf_recognition_request:
         ~azure.communication.callautomation.models.ContinuousDtmfRecognitionRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(continuous_dtmf_recognition_request, (IOBase, bytes)):
            _content = continuous_dtmf_recognition_request
        else:
            _json = self._serialize.body(continuous_dtmf_recognition_request, "ContinuousDtmfRecognitionRequest")

        _request = build_call_media_start_continuous_dtmf_recognition_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def stop_continuous_dtmf_recognition(
        self,
        call_connection_id: str,
        continuous_dtmf_recognition_request: _models.ContinuousDtmfRecognitionRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Stop continuous Dtmf recognition by unsubscribing to tones.

        Stop continuous Dtmf recognition by unsubscribing to tones.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param continuous_dtmf_recognition_request: The continuous recognize request. Required.
        :type continuous_dtmf_recognition_request:
         ~azure.communication.callautomation.models.ContinuousDtmfRecognitionRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def stop_continuous_dtmf_recognition(
        self,
        call_connection_id: str,
        continuous_dtmf_recognition_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Stop continuous Dtmf recognition by unsubscribing to tones.

        Stop continuous Dtmf recognition by unsubscribing to tones.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param continuous_dtmf_recognition_request: The continuous recognize request. Required.
        :type continuous_dtmf_recognition_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def stop_continuous_dtmf_recognition(  # pylint: disable=inconsistent-return-statements
        self,
        call_connection_id: str,
        continuous_dtmf_recognition_request: Union[_models.ContinuousDtmfRecognitionRequest, IO[bytes]],
        **kwargs: Any,
    ) -> None:
        """Stop continuous Dtmf recognition by unsubscribing to tones.

        Stop continuous Dtmf recognition by unsubscribing to tones.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param continuous_dtmf_recognition_request: The continuous recognize request. Is either a
         ContinuousDtmfRecognitionRequest type or a IO[bytes] type. Required.
        :type continuous_dtmf_recognition_request:
         ~azure.communication.callautomation.models.ContinuousDtmfRecognitionRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(continuous_dtmf_recognition_request, (IOBase, bytes)):
            _content = continuous_dtmf_recognition_request
        else:
            _json = self._serialize.body(continuous_dtmf_recognition_request, "ContinuousDtmfRecognitionRequest")

        _request = build_call_media_stop_continuous_dtmf_recognition_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def send_dtmf_tones(
        self,
        call_connection_id: str,
        send_dtmf_tones_request: _models.SendDtmfTonesRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.SendDtmfTonesResult:
        """Send dtmf tones.

        Send dtmf tones.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param send_dtmf_tones_request: The send dtmf tones request. Required.
        :type send_dtmf_tones_request: ~azure.communication.callautomation.models.SendDtmfTonesRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: SendDtmfTonesResult
        :rtype: ~azure.communication.callautomation.models.SendDtmfTonesResult
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def send_dtmf_tones(
        self,
        call_connection_id: str,
        send_dtmf_tones_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.SendDtmfTonesResult:
        """Send dtmf tones.

        Send dtmf tones.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param send_dtmf_tones_request: The send dtmf tones request. Required.
        :type send_dtmf_tones_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: SendDtmfTonesResult
        :rtype: ~azure.communication.callautomation.models.SendDtmfTonesResult
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def send_dtmf_tones(
        self,
        call_connection_id: str,
        send_dtmf_tones_request: Union[_models.SendDtmfTonesRequest, IO[bytes]],
        **kwargs: Any,
    ) -> _models.SendDtmfTonesResult:
        """Send dtmf tones.

        Send dtmf tones.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param send_dtmf_tones_request: The send dtmf tones request. Is either a SendDtmfTonesRequest
         type or a IO[bytes] type. Required.
        :type send_dtmf_tones_request: ~azure.communication.callautomation.models.SendDtmfTonesRequest
         or IO[bytes]
        :return: SendDtmfTonesResult
        :rtype: ~azure.communication.callautomation.models.SendDtmfTonesResult
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.SendDtmfTonesResult] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(send_dtmf_tones_request, (IOBase, bytes)):
            _content = send_dtmf_tones_request
        else:
            _json = self._serialize.body(send_dtmf_tones_request, "SendDtmfTonesRequest")

        _request = build_call_media_send_dtmf_tones_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("SendDtmfTonesResult", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @overload
    def hold(
        self,
        call_connection_id: str,
        hold_request: _models.HoldRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Hold participant from the call using identifier.

        Hold participant from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param hold_request: The participants to be hold from the call. Required.
        :type hold_request: ~azure.communication.callautomation.models.HoldRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def hold(
        self, call_connection_id: str, hold_request: IO[bytes], *, content_type: str = "application/json", **kwargs: Any
    ) -> None:
        """Hold participant from the call using identifier.

        Hold participant from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param hold_request: The participants to be hold from the call. Required.
        :type hold_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def hold(  # pylint: disable=inconsistent-return-statements
        self, call_connection_id: str, hold_request: Union[_models.HoldRequest, IO[bytes]], **kwargs: Any
    ) -> None:
        """Hold participant from the call using identifier.

        Hold participant from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param hold_request: The participants to be hold from the call. Is either a HoldRequest type or
         a IO[bytes] type. Required.
        :type hold_request: ~azure.communication.callautomation.models.HoldRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(hold_request, (IOBase, bytes)):
            _content = hold_request
        else:
            _json = self._serialize.body(hold_request, "HoldRequest")

        _request = build_call_media_hold_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def unhold(
        self,
        call_connection_id: str,
        unhold_request: _models.UnholdRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Unhold participants from the call using identifier.

        Unhold participants from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param unhold_request: The participants to be hold from the call. Required.
        :type unhold_request: ~azure.communication.callautomation.models.UnholdRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def unhold(
        self,
        call_connection_id: str,
        unhold_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Unhold participants from the call using identifier.

        Unhold participants from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param unhold_request: The participants to be hold from the call. Required.
        :type unhold_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def unhold(  # pylint: disable=inconsistent-return-statements
        self, call_connection_id: str, unhold_request: Union[_models.UnholdRequest, IO[bytes]], **kwargs: Any
    ) -> None:
        """Unhold participants from the call using identifier.

        Unhold participants from the call using identifier.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param unhold_request: The participants to be hold from the call. Is either a UnholdRequest
         type or a IO[bytes] type. Required.
        :type unhold_request: ~azure.communication.callautomation.models.UnholdRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(unhold_request, (IOBase, bytes)):
            _content = unhold_request
        else:
            _json = self._serialize.body(unhold_request, "UnholdRequest")

        _request = build_call_media_unhold_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def start_media_streaming(
        self,
        call_connection_id: str,
        start_media_streaming_request: _models.StartMediaStreamingRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Starts media streaming in the call.

        Starts media streaming in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param start_media_streaming_request: Required.
        :type start_media_streaming_request:
         ~azure.communication.callautomation.models.StartMediaStreamingRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def start_media_streaming(
        self,
        call_connection_id: str,
        start_media_streaming_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Starts media streaming in the call.

        Starts media streaming in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param start_media_streaming_request: Required.
        :type start_media_streaming_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def start_media_streaming(  # pylint: disable=inconsistent-return-statements
        self,
        call_connection_id: str,
        start_media_streaming_request: Union[_models.StartMediaStreamingRequest, IO[bytes]],
        **kwargs: Any,
    ) -> None:
        """Starts media streaming in the call.

        Starts media streaming in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param start_media_streaming_request: Is either a StartMediaStreamingRequest type or a
         IO[bytes] type. Required.
        :type start_media_streaming_request:
         ~azure.communication.callautomation.models.StartMediaStreamingRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(start_media_streaming_request, (IOBase, bytes)):
            _content = start_media_streaming_request
        else:
            _json = self._serialize.body(start_media_streaming_request, "StartMediaStreamingRequest")

        _request = build_call_media_start_media_streaming_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @overload
    def stop_media_streaming(
        self,
        call_connection_id: str,
        stop_media_streaming_request: _models.StopMediaStreamingRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Stops media streaming in the call.

        Stops media streaming in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param stop_media_streaming_request: stop media streaming request payload. Required.
        :type stop_media_streaming_request:
         ~azure.communication.callautomation.models.StopMediaStreamingRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def stop_media_streaming(
        self,
        call_connection_id: str,
        stop_media_streaming_request: IO[bytes],
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> None:
        """Stops media streaming in the call.

        Stops media streaming in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param stop_media_streaming_request: stop media streaming request payload. Required.
        :type stop_media_streaming_request: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def stop_media_streaming(  # pylint: disable=inconsistent-return-statements
        self,
        call_connection_id: str,
        stop_media_streaming_request: Union[_models.StopMediaStreamingRequest, IO[bytes]],
        **kwargs: Any,
    ) -> None:
        """Stops media streaming in the call.

        Stops media streaming in the call.

        :param call_connection_id: The call connection id. Required.
        :type call_connection_id: str
        :param stop_media_streaming_request: stop media streaming request payload. Is either a
         StopMediaStreamingRequest type or a IO[bytes] type. Required.
        :type stop_media_streaming_request:
         ~azure.communication.callautomation.models.StopMediaStreamingRequest or IO[bytes]
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[None] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(stop_media_streaming_request, (IOBase, bytes)):
            _content = stop_media_streaming_request
        else:
            _json = self._serialize.body(stop_media_streaming_request, "StopMediaStreamingRequest")

        _request = build_call_media_stop_media_streaming_request(
            call_connection_id=call_connection_id,
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore


class CallRecordingOperations:
    """
    .. warning::
        **DO NOT** instantiate this class directly.

        Instead, you should access the following operations through
        :class:`~azure.communication.callautomation.AzureCommunicationCallAutomationService`'s
        :attr:`call_recording` attribute.
    """

    models = _models

    def __init__(self, *args, **kwargs) -> None:
        input_args = list(args)
        self._client: PipelineClient = input_args.pop(0) if input_args else kwargs.pop("client")
        self._config: AzureCommunicationCallAutomationServiceConfiguration = (
            input_args.pop(0) if input_args else kwargs.pop("config")
        )
        self._serialize: Serializer = input_args.pop(0) if input_args else kwargs.pop("serializer")
        self._deserialize: Deserializer = input_args.pop(0) if input_args else kwargs.pop("deserializer")

    @overload
    def start_recording(
        self,
        start_call_recording: _models.StartCallRecordingRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any,
    ) -> _models.RecordingStateResponse:
        """Start recording the call.

        Start recording the call.

        :param start_call_recording: The request body of start call recording request. Required.
        :type start_call_recording:
         ~azure.communication.callautomation.models.StartCallRecordingRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: RecordingStateResponse
        :rtype: ~azure.communication.callautomation.models.RecordingStateResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    def start_recording(
        self, start_call_recording: IO[bytes], *, content_type: str = "application/json", **kwargs: Any
    ) -> _models.RecordingStateResponse:
        """Start recording the call.

        Start recording the call.

        :param start_call_recording: The request body of start call recording request. Required.
        :type start_call_recording: IO[bytes]
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :return: RecordingStateResponse
        :rtype: ~azure.communication.callautomation.models.RecordingStateResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace
    def start_recording(
        self, start_call_recording: Union[_models.StartCallRecordingRequest, IO[bytes]], **kwargs: Any
    ) -> _models.RecordingStateResponse:
        """Start recording the call.

        Start recording the call.

        :param start_call_recording: The request body of start call recording request. Is either a
         StartCallRecordingRequest type or a IO[bytes] type. Required.
        :type start_call_recording:
         ~azure.communication.callautomation.models.StartCallRecordingRequest or IO[bytes]
        :return: RecordingStateResponse
        :rtype: ~azure.communication.callautomation.models.RecordingStateResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.RecordingStateResponse] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(start_call_recording, (IOBase, bytes)):
            _content = start_call_recording
        else:
            _json = self._serialize.body(start_call_recording, "StartCallRecordingRequest")

        _request = build_call_recording_start_recording_request(
            content_type=content_type,
            api_version=self._config.api_version,
            json=_json,
            content=_content,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("RecordingStateResponse", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def get_recording_properties(self, recording_id: str, **kwargs: Any) -> _models.RecordingStateResponse:
        """Get call recording properties.

        Get call recording properties.

        :param recording_id: The recording id. Required.
        :type recording_id: str
        :return: RecordingStateResponse
        :rtype: ~azure.communication.callautomation.models.RecordingStateResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[_models.RecordingStateResponse] = kwargs.pop("cls", None)

        _request = build_call_recording_get_recording_properties_request(
            recording_id=recording_id,
            api_version=self._config.api_version,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        deserialized = self._deserialize("RecordingStateResponse", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace
    def stop_recording(  # pylint: disable=inconsistent-return-statements
        self, recording_id: str, **kwargs: Any
    ) -> None:
        """Stop recording the call.

        Stop recording the call.

        :param recording_id: The recording id. Required.
        :type recording_id: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_call_recording_stop_recording_request(
            recording_id=recording_id,
            api_version=self._config.api_version,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @distributed_trace
    def pause_recording(  # pylint: disable=inconsistent-return-statements
        self, recording_id: str, **kwargs: Any
    ) -> None:
        """Pause recording the call.

        Pause recording the call.

        :param recording_id: The recording id. Required.
        :type recording_id: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_call_recording_pause_recording_request(
            recording_id=recording_id,
            api_version=self._config.api_version,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore

    @distributed_trace
    def resume_recording(  # pylint: disable=inconsistent-return-statements
        self, recording_id: str, **kwargs: Any
    ) -> None:
        """Resume recording the call.

        Resume recording the call.

        :param recording_id: The recording id. Required.
        :type recording_id: str
        :return: None
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = kwargs.pop("params", {}) or {}

        cls: ClsType[None] = kwargs.pop("cls", None)

        _request = build_call_recording_resume_recording_request(
            recording_id=recording_id,
            api_version=self._config.api_version,
            headers=_headers,
            params=_params,
        )
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }
        _request.url = self._client.format_url(_request.url, **path_format_arguments)

        _stream = False
        pipeline_response: PipelineResponse = self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.CommunicationErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        if cls:
            return cls(pipeline_response, None, {})  # type: ignore
