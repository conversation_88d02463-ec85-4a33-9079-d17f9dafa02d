/**
 * @module botframework-streaming
 */
/**
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import WebSocket from 'ws';
import { INodeIncomingMessage, INodeBuffer, INodeSocket, ISocket } from '../interfaces';
/**
 * An implementation of [ISocket](xref:botframework-streaming.ISocket) to use with a [NodeWebSocketFactory](xref:botframework-streaming.NodeWebSocketFactory) to create a WebSocket server.
 */
export declare class NodeWebSocket implements ISocket {
    private wsSocket?;
    protected wsServer: WebSocket.Server;
    /**
     * Creates a new [NodeWebSocket](xref:botframework-streaming.NodeWebSocket) instance.
     *
     * @param wsSocket The `ws` WebSocket instance to build this connection on.
     */
    constructor(wsSocket?: WebSocket);
    /**
     * Create and set a `ws` WebSocket with an HTTP Request, Socket and Buffer.
     *
     * @param req An HTTP Request matching the [INodeIncomingMessage](xref:botframework-streaming.INodeIncomingMessage) interface.
     * @param socket A Socket [INodeSocket](xref:botframework-streaming.INodeSocket) interface.
     * @param head A Buffer [INodeBuffer](xref:botframework-streaming.INodeBuffer) interface.
     * @returns A Promise that resolves after the WebSocket upgrade has been handled, otherwise rejects with a thrown error.
     */
    create(req: INodeIncomingMessage, socket: INodeSocket, head: INodeBuffer): Promise<void>;
    /*
    * Indicates if the 'ws' WebSocket is currently connected and ready to send messages.
    *
    * @returns `true` if the underlying websocket is ready and availble to send messages, otherwise `false`.
    */
    readonly isConnected: boolean;
    /**
     * Writes a buffer to the socket and sends it.
     *
     * @param buffer The buffer of data to send across the connection.
     */
    write(buffer: INodeBuffer): void;
    /**
     * Connects to the supporting socket using WebSocket protocol.
     *
     * @param serverAddressOrHostName The host name or URL the server is listening on.
     * @param port If `serverAddressOrHostName` is a host name, the port the server is listening on, defaults to 8082. Otherwise, this argument is ignored.
     * @returns A Promise that resolves when the websocket connection is closed, or rejects on an error.
     */
    connect(serverAddressOrHostName: string, port?: number): Promise<void>;
    /**
     * Set the handler for `'message'` events received on the socket.
     *
     * @param handler The callback to handle the "message" event.
     */
    setOnMessageHandler(handler: (x: any) => void): void;
    /**
     * Close the socket.
     *
     * @remarks
     * Optionally pass in a status code and string explaining why the connection is closing.
     * @param code Optional status code to explain why the connection has closed.
     * @param data Optional additional data to explain why the connection has closed.
     */
    close(code?: number, data?: string): void;
    /**
     * Set the callback to call when encountering socket closures.
     *
     * @param handler The callback to handle the "close" event.
     */
    setOnCloseHandler(handler: (x: any) => void): void;
    /**
     * Set the callback to call when encountering errors.
     *
     * @param handler The callback to handle the "error" event.
     */
    setOnErrorHandler(handler: (x: any) => void): void;
}
//# sourceMappingURL=nodeWebSocket.d.ts.map
