#!/usr/bin/env python3
"""
Installation script for Real Teams Integration
This script installs all required dependencies for production Teams meeting joining
"""

import subprocess
import sys
import os
import platform

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def install_dependencies():
    """Install all required dependencies"""
    print("📦 Installing production dependencies...")

    dependencies = [
        "msgraph-core>=0.2.2",
        "azure-identity>=1.15.0",
        "websockets>=12.0",
        "pyaudio>=0.2.11",
        "openai>=1.0.0",
        "deepgram-sdk>=3.0.0",
        "azure-cognitiveservices-speech>=1.34.0",
        "requests>=2.31.0",
        "aiofiles>=23.0.0"
    ]

    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            return False

    return True

def check_audio_system():
    """Check if audio system is properly configured"""
    print("\n🎤 Checking audio system...")

    try:
        import pyaudio
        p = pyaudio.PyAudio()

        # Check for input devices
        input_devices = []
        for i in range(p.get_device_count()):
            info = p.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                input_devices.append(info['name'])

        p.terminate()

        if input_devices:
            print(f"✅ Found {len(input_devices)} audio input devices")
            print(f"Available devices: {', '.join(input_devices[:3])}...")
            return True
        else:
            print("⚠️ No audio input devices found")
            return False

    except Exception as e:
        print(f"❌ Audio system check failed: {e}")
        return False

def verify_environment():
    """Verify all required environment variables are set"""
    print("\n🔍 Verifying environment variables...")

    required_vars = [
        "CLIENT_ID",
        "CLIENT_SECRET",
        "TENANT_ID",
        "USER_OBJECT_ID",
        "ACS_CONNECTION_STRING",
        "SPEECH_KEY",
        "SPEECH_REGION",
        "BOT_DOMAIN"
    ]

    optional_vars = [
        "OPENAI_API_KEY",
        "DEEPGRAM_API_KEY"
    ]

    missing_required = []
    missing_optional = []

    for var in required_vars:
        if not os.getenv(var):
            missing_required.append(var)
        else:
            print(f"✅ {var}: Set")

    for var in optional_vars:
        if not os.getenv(var):
            missing_optional.append(var)
        else:
            print(f"✅ {var}: Set")

    if missing_required:
        print(f"\n❌ Missing required environment variables: {', '.join(missing_required)}")
        print("Please add these to your .env file")
        return False

    if missing_optional:
        print(f"\n⚠️ Missing optional environment variables: {', '.join(missing_optional)}")
        print("These are optional but recommended for better transcription")

    print("\n✅ All required environment variables are set!")
    return True

def test_graph_api_connection():
    """Test Graph API connection"""
    print("\n🔗 Testing Graph API connection...")

    try:
        from azure.identity import ClientSecretCredential

        client_id = os.getenv("CLIENT_ID")
        client_secret = os.getenv("CLIENT_SECRET")
        tenant_id = os.getenv("TENANT_ID")

        if not all([client_id, client_secret, tenant_id]):
            print("❌ Missing Graph API credentials")
            return False

        credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )

        # Try to get a token
        token = credential.get_token("https://graph.microsoft.com/.default")

        if token and token.token:
            print("✅ Graph API connection successful!")
            return True
        else:
            print("❌ Failed to get Graph API token")
            return False

    except Exception as e:
        print(f"❌ Graph API connection failed: {e}")
        return False

def main():
    """Main installation function"""
    print("🚀 Real Teams Integration Setup")
    print("=" * 50)

    success = True

    # Install dependencies
    if not install_dependencies():
        success = False

    # Check audio system
    if not check_audio_system():
        print("⚠️ Audio system issues detected. Teams audio may not work properly.")

    # Verify environment
    if not verify_environment():
        success = False

    # Test Graph API
    if success and not test_graph_api_connection():
        success = False

    print("\n" + "=" * 50)
    if success:
        print("🎉 Production setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Start your bot: uvicorn teamsbotcau:app --host 0.0.0.0 --port 8000")
        print("2. Join a Teams meeting: POST /join-meeting")
        print("3. Check status: GET /meeting-status/{meeting_id}")
        print("4. View live transcription: GET /live-transcription/{meeting_id}")
        print("\n🎯 Your bot will now:")
        print("✅ Actually join Teams meetings as a visible participant")
        print("✅ Record live audio streams")
        print("✅ Provide real-time transcription")
        print("✅ Save transcriptions to local files")
        print("\n💡 Monitor logs for real-time status updates!")
    else:
        print("❌ Setup encountered issues. Please resolve them and try again.")
        print("\n🔧 Common fixes:")
        print("- Ensure all environment variables are set in .env file")
        print("- Check Azure app registration permissions")
        print("- Verify audio drivers are installed")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
