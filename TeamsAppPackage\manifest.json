{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json", "manifestVersion": "1.16", "version": "1.0.0", "id": "4a521234-7111-46a9-a2fb-a6f69255dd3a", "packageName": "com.company.teamsbot", "developer": {"name": "AI Meeting Assistant", "websiteUrl": "https://8c13-20-***********.ngrok-free.app", "privacyUrl": "https://8c13-20-***********.ngrok-free.app/privacy", "termsOfUseUrl": "https://8c13-20-***********.ngrok-free.app/terms"}, "icons": {"color": "color.png", "outline": "outline.png"}, "name": {"short": "AI Meeting Assistant", "full": "AI Meeting Assistant - Transcription <PERSON><PERSON>"}, "description": {"short": "AI-powered meeting transcription assistant", "full": "An AI-powered bot that joins Teams meetings to provide real-time transcription and meeting assistance."}, "accentColor": "#FFFFFF", "bots": [{"botId": "4a521234-7111-46a9-a2fb-a6f69255dd3a", "scopes": ["personal", "team", "groupchat"], "supportsFiles": false, "isNotificationOnly": false, "supportsCalling": true, "supportsVideo": false, "commandLists": [{"scopes": ["personal", "team", "groupchat"], "commands": [{"title": "Join Meeting", "description": "Join a Teams meeting for transcription"}, {"title": "Help", "description": "Get help with meeting transcription"}]}]}], "configurableTabs": [], "staticTabs": [], "composeExtensions": [], "permissions": ["identity", "messageTeamMembers"], "devicePermissions": ["media"], "validDomains": ["8978-20-***********.ngrok-free.app"], "webApplicationInfo": {"id": "4a521234-7111-46a9-a2fb-a6f69255dd3a", "resource": "https://graph.microsoft.com"}}