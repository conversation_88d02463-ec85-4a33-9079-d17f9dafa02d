# Teams Calling Bot Configuration
CLIENT_ID=4a521234-7111-46a9-a2fb-a6f69255dd3a
CLIENT_SECRET=****************************************
TENANT_ID=9750f5c0-d037-4115-a5b2-b25c5994ed60
BOT_DOMAIN=https://8c13-20-169-212-255.ngrok-free.app
PORT=3978
ACS_CONNECTION_STRING='endpoint=https://mindlabz-bot-communication.unitedstates.communication.azure.com/;accesskey=5OU5xwF9W5rTNhU3yuseAsWYsZydoy2jcPxKBpSiLUvZQeUrUdp0JQQJ99BFACULyCpL27H0AAAAAZCSZyuB'

# Python Backend Integration
PYTHON_BACKEND_URL=http://localhost:8000

# Optional: Logging level
LOG_LEVEL=info
