/**
 * @module botframework-streaming
 */
/**
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import { IReceiveResponse } from '../interfaces';
/**
 * Orchestrates and manages pending streaming requests.
 */
export declare class RequestManager {
    private readonly _pendingRequests;
    /**
     * Gets the count of the pending requests.
     *
     * @returns Number with the pending requests count.
     */
    pendingRequestCount(): number;
    /**
     * Signal fired when all response tasks have completed.
     *
     * @param requestId The ID of the StreamingRequest.
     * @param response The [IReceiveResponse](xref:botframework-streaming.IReceiveResponse) in response to the request.
     * @returns A Promise that when completed returns `true` if the `requestId`'s pending response task was completed, otherwise `false`.
     */
    signalResponse(requestId: string, response: IReceiveResponse): Promise<boolean>;
    /**
     * Constructs and returns a response for this request.
     *
     * @param requestId The ID of the StreamingRequest being responded to.
     * @returns The response to the specified request.
     */
    getResponse(requestId: string): Promise<IReceiveResponse>;
    /**
     * Rejects all requests pending a response.
     *
     * @param reason The reason for rejection.
     */
    rejectAllResponses(reason?: Error): void;
}
//# sourceMappingURL=requestManager.d.ts.map
