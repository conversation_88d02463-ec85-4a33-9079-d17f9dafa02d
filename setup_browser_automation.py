#!/usr/bin/env python3
"""
Setup script for Teams Bot Browser Automation
This script installs the required dependencies and sets up Chrome WebDriver
"""

import subprocess
import sys
import os
import platform

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def install_selenium():
    """Install Selenium and WebDriver Manager"""
    print("📦 Installing Selenium and WebDriver Manager...")
    
    commands = [
        ("pip install selenium>=4.15.0", "Installing Selenium"),
        ("pip install webdriver-manager>=4.0.0", "Installing WebDriver Manager"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    
    return True

def setup_chrome_driver():
    """Setup Chrome WebDriver"""
    print("\n🌐 Setting up Chrome WebDriver...")
    
    # Check if Chrome is installed
    system = platform.system().lower()
    
    if system == "windows":
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        ]
    elif system == "darwin":  # macOS
        chrome_paths = ["/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"]
    else:  # Linux
        chrome_paths = ["/usr/bin/google-chrome", "/usr/bin/chromium-browser"]
    
    chrome_found = any(os.path.exists(path) for path in chrome_paths)
    
    if not chrome_found:
        print("⚠️  Google Chrome not found. Please install Google Chrome:")
        print("   Windows: https://www.google.com/chrome/")
        print("   macOS: https://www.google.com/chrome/")
        print("   Linux: sudo apt-get install google-chrome-stable")
        return False
    
    print("✅ Google Chrome found")
    
    # Test WebDriver setup
    try:
        print("🧪 Testing WebDriver setup...")
        test_code = '''
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

options = Options()
options.add_argument("--headless")
options.add_argument("--no-sandbox")
options.add_argument("--disable-dev-shm-usage")

service = Service(ChromeDriverManager().install())
driver = webdriver.Chrome(service=service, options=options)
driver.get("https://www.google.com")
print("WebDriver test successful!")
driver.quit()
'''
        
        with open("test_webdriver.py", "w") as f:
            f.write(test_code)
        
        result = subprocess.run([sys.executable, "test_webdriver.py"], 
                              capture_output=True, text=True, timeout=60)
        
        os.remove("test_webdriver.py")
        
        if result.returncode == 0:
            print("✅ WebDriver setup test passed")
            return True
        else:
            print(f"❌ WebDriver test failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ WebDriver test failed: {e}")
        return False

def update_env_file():
    """Update .env file with browser automation settings"""
    print("\n📝 Updating .env file...")
    
    env_additions = """
# Browser Automation Settings
HEADLESS=false
TEAMS_GUEST_NAME=AIMeetingAssistant
"""
    
    try:
        # Check if .env exists
        if os.path.exists(".env"):
            with open(".env", "r") as f:
                content = f.read()
            
            # Only add if not already present
            if "HEADLESS=" not in content:
                with open(".env", "a") as f:
                    f.write(env_additions)
                print("✅ Added browser automation settings to .env")
            else:
                print("✅ Browser automation settings already present in .env")
        else:
            print("⚠️  .env file not found. Please create it with the required settings.")
            
    except Exception as e:
        print(f"❌ Failed to update .env file: {e}")

def main():
    """Main setup function"""
    print("🤖 Teams Bot Browser Automation Setup")
    print("=" * 50)
    
    success = True
    
    # Install Python dependencies
    if not install_selenium():
        success = False
    
    # Setup Chrome WebDriver
    if not setup_chrome_driver():
        success = False
    
    # Update environment file
    update_env_file()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Make sure Google Chrome is installed")
        print("2. Run your Teams bot: uvicorn teamsbotcau:app --host 0.0.0.0 --port 8000")
        print("3. The bot will now actually join Teams meetings as a visible participant!")
        print("\n💡 Tips:")
        print("- Set HEADLESS=true in .env to run browser in background")
        print("- Set TEAMS_GUEST_NAME in .env to customize bot display name")
    else:
        print("❌ Setup encountered some issues. Please resolve them and try again.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
