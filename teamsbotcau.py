import os
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import uuid
import threading
import re
import time
import wave
import aiohttp
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
from pydantic import BaseModel
from azure.communication.callautomation import CallAutomationClient
import msal

from dotenv import load_dotenv
from teams_integration import RealTeamsIntegration
from teams_bot_framework import TeamsBotFrameworkIntegration
from teams_webhook_integration import TeamsWebhookIntegration
from real_teams_bot import RealTeamsBot
from transcription_service import RealTimeTranscriptionService

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global bot instances
teams_bot = None
real_teams_integration = None
teams_bot_framework = None
teams_webhook_integration = None
real_teams_bot = None
transcription_service = None

@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup
    global teams_bot, real_teams_integration, teams_bot_framework, teams_webhook_integration, real_teams_bot, transcription_service

    # Initialize services
    real_teams_integration = RealTeamsIntegration()
    teams_bot_framework = TeamsBotFrameworkIntegration()
    teams_webhook_integration = TeamsWebhookIntegration()
    real_teams_bot = RealTeamsBot()
    transcription_service = RealTimeTranscriptionService()
    teams_bot = ModernTeamsBot()

    logger.info("Real Teams integration, Bot Framework, Webhook integration, Real Teams Bot, and transcription services initialized")
    yield

    # Shutdown
    if teams_bot:
        # Clean up any active sessions
        for session in teams_bot.active_sessions.values():
            try:
                await session.stop_transcription()
            except Exception as e:
                logger.error(f"Error stopping session during shutdown: {e}")

    # Clean up real Teams calls
    if real_teams_integration:
        active_calls = real_teams_integration.get_active_calls()
        for call_id in active_calls.keys():
            try:
                await real_teams_integration.leave_meeting(call_id)
            except Exception as e:
                logger.error(f"Error leaving call {call_id} during shutdown: {e}")

app = FastAPI(
    title="Teams Meeting Bot with Modern Azure SDKs",
    version="2.0.0",
    lifespan=lifespan
)

# Environment variables
CLIENT_ID = os.getenv("CLIENT_ID")
CLIENT_SECRET = os.getenv("CLIENT_SECRET")
TENANT_ID = os.getenv("TENANT_ID")
SPEECH_KEY = os.getenv("SPEECH_KEY")
SPEECH_REGION = os.getenv("SPEECH_REGION")
ACS_CONNECTION_STRING = os.getenv("ACS_CONNECTION_STRING", "")
USER_OBJECT_ID = os.getenv("USER_OBJECT_ID")
BOT_DOMAIN = 'https://8978-20-169-212-255.ngrok-free.app'#os.getenv("BOT_DOMAIN", "https://localhost:8000")

# Validate required environment variables
required_vars = {
    "CLIENT_ID": CLIENT_ID,
    "CLIENT_SECRET": CLIENT_SECRET,
    "TENANT_ID": TENANT_ID,
    "USER_OBJECT_ID": USER_OBJECT_ID
}

missing_vars = [var for var, value in required_vars.items() if not value]
if missing_vars:
    logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
    raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

# Log environment variables for debugging
logger.info(f"Environment: CLIENT_ID={len(CLIENT_ID) if CLIENT_ID else 0} chars, TENANT_ID={TENANT_ID}, SPEECH_KEY={len(SPEECH_KEY) if SPEECH_KEY else 0} chars, SPEECH_REGION={SPEECH_REGION}, ACS_CONNECTION_STRING={len(ACS_CONNECTION_STRING) if ACS_CONNECTION_STRING else 0} chars, BOT_DOMAIN={BOT_DOMAIN}")

if not ACS_CONNECTION_STRING:
    logger.warning("ACS_CONNECTION_STRING not found. Bot functionality will be limited.")

if not SPEECH_KEY or not SPEECH_REGION:
    logger.warning("SPEECH_KEY or SPEECH_REGION not found. Speech transcription will be limited.")

# Microsoft Graph API settings
GRAPH_API_ENDPOINT = "https://graph.microsoft.com/v1.0"
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPES = ["https://graph.microsoft.com/.default"]

class MeetingRequest(BaseModel):
    meeting_url: str
    meeting_id: Optional[str] = None
    display_name: Optional[str] = "Transcription Bot"
    enable_recording: Optional[bool] = True

class GraphAPIClient:
    def __init__(self):
        self.app = msal.ConfidentialClientApplication(
            CLIENT_ID,
            authority=AUTHORITY,
            client_credential=CLIENT_SECRET,
        )
        self.access_token = None
        self.token_expires_at = 0

    async def get_access_token(self):
        """Get Microsoft Graph API access token"""
        logger.info("Attempting to acquire Graph API token")
        if self.access_token and time.time() < self.token_expires_at:
            logger.info("Using cached token")
            return self.access_token

        try:
            result = self.app.acquire_token_silent(SCOPES, account=None)
            if not result:
                logger.info("No silent token, acquiring new token")
                result = self.app.acquire_token_for_client(scopes=SCOPES)

            if "access_token" in result:
                self.access_token = result["access_token"]
                self.token_expires_at = time.time() + result.get("expires_in", 3600) - 300
                logger.info("Token acquired successfully")
                return self.access_token
            else:
                logger.error(f"Token acquisition failed: {result}")
                raise Exception(f"Failed to get access token: {result.get('error_description')}")
        except Exception as e:
            logger.error(f"Token acquisition error: {e}")
            raise

    async def get_meeting_info(self, meeting_url: str):
        """Extract meeting information from Teams meeting URL"""
        try:
            token = await self.get_access_token()
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

            # Extract meeting ID from URL
            meeting_id = self.extract_meeting_id_from_url(meeting_url)
            logger.info(f"Extracted meeting ID: {meeting_id} from URL: {meeting_url}")

            async with aiohttp.ClientSession() as session:
                # Use /onlineMeetings instead of /me/onlineMeetings
                url = f"{GRAPH_API_ENDPOINT}/users/{USER_OBJECT_ID}/onlineMeetings/{meeting_id}"
                logger.info(f"Sending Graph API request to: {url}")
                async with session.get(url, headers=headers) as response:
                    response_text = await response.text()
                    logger.info(f"Graph API response status: {response.status}, body: {response_text}")
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"Could not fetch meeting details: {response.status}")
                        return {"id": meeting_id, "joinUrl": meeting_url}

        except Exception as e:
            logger.error(f"Error getting meeting info: {e}")
            return {"id": str(uuid.uuid4()), "joinUrl": meeting_url}

    def extract_meeting_id_from_url(self, meeting_url: str) -> str:
        """Extract meeting ID from Teams meeting URL"""
        patterns = [
            r'meetup-join/([^/?]+)',
            r'meeting/([^/?]+)',
            r'join/([^/?]+)',
            r'[?&]meetingID=([^&]+)',
            r'[?&]threadId=([^&]+)',
            r'/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})',
        ]

        for pattern in patterns:
            match = re.search(pattern, meeting_url, re.IGNORECASE)
            if match:
                return match.group(1)

        return str(uuid.uuid4())

class AudioStreamRecorder:
    def __init__(self, filename: str, sample_rate: int = 16000, channels: int = 1):
        self.filename = filename
        self.sample_rate = sample_rate
        self.channels = channels
        self.frames = []
        self.is_recording = False
        self.audio_lock = threading.Lock()

    def start_recording(self):
        self.is_recording = True
        self.frames = []
        logger.info(f"Started recording audio to {self.filename}")

    def add_audio_data(self, audio_data: bytes):
        if self.is_recording:
            with self.audio_lock:
                self.frames.append(audio_data)

    def stop_recording(self):
        self.is_recording = False
        self.save_audio()

    def save_audio(self):
        if not self.frames:
            logger.warning("No audio frames to save")
            return

        try:
            with wave.open(self.filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(2)  # 16-bit audio
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(self.frames))
            logger.info(f"Audio saved to {self.filename}")
        except Exception as e:
            logger.error(f"Error saving audio: {e}")

class ModernTranscriptionSession:
    def __init__(self, meeting_id: str, meeting_info: Dict[str, Any], display_name: str = "Bot"):
        self.meeting_id = meeting_id
        self.meeting_info = meeting_info
        self.display_name = display_name
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.audio_file = f"audio_{meeting_id}_{timestamp}.wav"
        self.is_active = False
        self.call_automation_client = None
        self.call_connection = None
        self.audio_recorder = AudioStreamRecorder(self.audio_file)
        self.graph_client = GraphAPIClient()
        self.recording_id = None

        # Real Teams integration
        self.real_call_id = None
        self.transcription_buffer = None
        self.is_real_teams_session = False

    async def start_transcription(self):
        """Start the recording session"""
        try:
            meeting_url = self.meeting_info.get('joinUrl') or self.meeting_info.get('join_url')

            # Try Real Teams Bot first (joins as visible participant)
            if meeting_url and real_teams_bot:
                logger.info("🤖 Attempting to join Teams meeting as visible participant...")
                result = await real_teams_bot.join_meeting_as_participant(
                    meeting_url,
                    self.display_name
                )

                if result.get("success"):
                    self.real_call_id = result.get("session_id")
                    self.is_real_teams_session = True
                    self.real_bot_session = True

                    # Setup transcription for real Teams session
                    if transcription_service:
                        self.transcription_buffer = transcription_service.create_session(self.meeting_id)

                    logger.info(f"✅ Successfully joined Teams meeting as visible participant! Session ID: {self.real_call_id}")
                    logger.info("🎉 Bot is now visible in the Teams meeting participant list!")
                else:
                    logger.warning(f"Real Teams Bot failed: {result.get('error')}")
                    logger.info("🔄 Trying Webhook approach...")

            # Try Webhook approach if Real Teams Bot failed
            if not self.is_real_teams_session and meeting_url and teams_webhook_integration:
                logger.info("🔗 Attempting to join Teams meeting using Webhook integration...")
                result = await teams_webhook_integration.join_meeting_via_webhook(
                    meeting_url,
                    self.display_name
                )

                if result.get("success"):
                    self.real_call_id = result.get("session_id")
                    self.is_real_teams_session = True
                    self.webhook_session = True

                    # Setup transcription for real Teams session
                    if transcription_service:
                        self.transcription_buffer = transcription_service.create_session(self.meeting_id)

                    logger.info(f"✅ Successfully connected to Teams meeting via Webhook! Session ID: {self.real_call_id}")
                    logger.info("🎉 Bot is now monitoring the Teams meeting!")
                else:
                    logger.warning(f"Webhook integration failed: {result.get('error')}")
                    logger.info("🔄 Trying Bot Framework...")

            # Try Bot Framework approach if Webhook failed
            if not self.is_real_teams_session and meeting_url and teams_bot_framework:
                logger.info("🤖 Attempting to join Teams meeting using Bot Framework...")
                result = await teams_bot_framework.join_meeting_via_bot_framework(
                    meeting_url,
                    self.display_name
                )

                if result.get("success"):
                    self.real_call_id = result.get("session_id")
                    self.is_real_teams_session = True
                    self.bot_framework_session = True

                    # Setup transcription for real Teams session
                    if transcription_service:
                        self.transcription_buffer = transcription_service.create_session(self.meeting_id)

                    logger.info(f"✅ Successfully joined Teams meeting via Bot Framework! Session ID: {self.real_call_id}")
                    logger.info("🎉 Bot is now visible in the Teams meeting as a participant!")
                else:
                    logger.warning(f"Bot Framework integration failed: {result.get('error')}")
                    logger.info("🔄 Trying Graph API Cloud Communications...")

            # Try Graph API Cloud Communications if previous methods failed
            if not self.is_real_teams_session and meeting_url and real_teams_integration:
                logger.info("📡 Attempting to join Teams meeting using Graph API...")
                result = await real_teams_integration.join_teams_meeting(
                    meeting_url,
                    self.display_name
                )

                if result.get("success"):
                    self.real_call_id = result.get("call_id")
                    self.is_real_teams_session = True
                    self.graph_api_session = True

                    # Setup transcription for real Teams session
                    if transcription_service:
                        self.transcription_buffer = transcription_service.create_session(self.meeting_id)

                    logger.info(f"✅ Successfully joined Teams meeting via Graph API! Call ID: {self.real_call_id}")
                    logger.info("🎉 Bot is now visible in the Teams meeting as a participant!")
                else:
                    logger.warning(f"Graph API integration failed: {result.get('error')}")
                    logger.warning("Falling back to mock implementation...")

            # Fallback to mock implementation if all real integrations failed
            if not self.is_real_teams_session:
                # Initialize Call Automation if ACS is configured
                if ACS_CONNECTION_STRING:
                    await self._setup_call_automation()

            self.is_active = True
            self.audio_recorder.start_recording()

            if self.is_real_teams_session:
                if getattr(self, 'real_bot_session', False):
                    approach = "Real Teams Bot (Visible Participant)"
                elif getattr(self, 'webhook_session', False):
                    approach = "Webhook"
                elif getattr(self, 'bot_framework_session', False):
                    approach = "Bot Framework"
                else:
                    approach = "Graph API"
                logger.info(f"✅ Successfully started REAL Teams session for meeting {self.meeting_id} via {approach}")
                logger.info("🎤 Live audio recording and transcription active!")
            else:
                logger.info(f"⚠️ Started MOCK session for meeting {self.meeting_id}")

        except Exception as e:
            logger.error(f"Error starting session: {e}")
            raise

    async def _setup_call_automation(self):
        """Setup Call Automation client for call management"""
        try:
            if not ACS_CONNECTION_STRING:
                logger.warning("ACS not configured, skipping call automation setup")
                return

            self.call_automation_client = CallAutomationClient.from_connection_string(ACS_CONNECTION_STRING)
            logger.info("Call Automation client initialized")

            # Get meeting URL and validate it
            if not hasattr(self, 'meeting_info') or not self.meeting_info:
                raise ValueError("Meeting info not available")

            meeting_url = self.meeting_info.get('joinUrl') or self.meeting_info.get('join_url')
            if not meeting_url:
                raise ValueError("Meeting URL not found in meeting info")

            logger.info(f"Joining meeting with URL: {meeting_url}")

            # IMPORTANT: Azure Call Automation API does NOT support joining Teams meetings via URL
            # This is a fundamental limitation of the current Azure SDK

            logger.warning("=== TEAMS MEETING LIMITATION ===")
            logger.warning("Azure Call Automation API cannot join Teams meetings directly")
            logger.warning("For PRODUCTION use, implement one of these approaches:")
            logger.warning("1. Microsoft Teams Bot Framework (Recommended)")
            logger.warning("2. Graph API Cloud Communications")
            logger.warning("3. ACS Teams Interop (requires special setup)")
            logger.warning("Current implementation uses MOCK connection for development")
            logger.warning("================================")

            # Create a mock call connection for development/testing
            self.call_connection = type('MockCallConnection', (), {
                'call_connection_id': f"mock_call_{self.meeting_id}",
                'hangup': self._mock_hangup,
                'is_mock': True,
                'production_ready': False
            })()
            logger.info(f"Created MOCK session for meeting {self.meeting_id} (NOT actually joined)")

            # Setup audio stream handling
            self._setup_audio_handlers()

        except Exception as e:
            logger.error(f"Error setting up call automation: {e}")
            raise

    async def _mock_hangup(self):
        """Mock hangup method for simulated call connection"""
        logger.info("Mock hangup called")

    def _setup_audio_handlers(self):
        """Setup audio stream handlers for the call"""
        try:
            if self.call_connection:
                logger.info(f"Call connection established with ID: {self.call_connection.call_connection_id}")
                logger.info("Audio stream handlers setup completed")
        except Exception as e:
            logger.error(f"Error setting up audio handlers: {e}")

    def process_audio_stream(self, audio_data: bytes):
        """Process incoming audio stream data"""
        try:
            # Save raw audio data
            self.audio_recorder.add_audio_data(audio_data)
        except Exception as e:
            logger.error(f"Error processing audio stream: {e}")

    async def start_recording(self):
        """Start call recording using Call Automation"""
        try:
            if not self.call_automation_client or not self.call_connection:
                logger.warning("Call Automation not available for recording")
                return

            # Check if this is a mock connection
            if hasattr(self.call_connection, 'call_connection_id') and 'mock_call_' in self.call_connection.call_connection_id:
                logger.info("Using mock recording for Teams meeting")
                self.recording_id = f"mock_recording_{self.meeting_id}"
                return

            # Start recording with the latest SDK parameters
            recording_params = {
                "recording_state_callback_url": f"{BOT_DOMAIN}/api/recording-status",
                "call_ids": [self.call_connection.call_connection_id],
                "content_type": "audio",
                "channel_type": "mixed",
                "format": "wav"
            }

            result = await self.call_automation_client.create_recording(**recording_params)
            if hasattr(result, 'recording_id'):
                self.recording_id = result.recording_id
                logger.info(f"Started recording with ID: {self.recording_id}")

        except Exception as e:
            logger.error(f"Error starting recording: {e}")

    async def stop_transcription(self):
        """Stop the recording session"""
        try:
            self.is_active = False
            self.audio_recorder.stop_recording()

            # Handle real Teams session cleanup
            if self.is_real_teams_session and self.real_call_id:
                if getattr(self, 'real_bot_session', False) and real_teams_bot:
                    logger.info("Leaving Real Teams Bot meeting...")
                    result = await real_teams_bot.leave_meeting(self.real_call_id)
                    if result.get("success"):
                        logger.info("✅ Successfully left Real Teams Bot meeting")
                    else:
                        logger.error(f"Error leaving Real Teams Bot meeting: {result.get('error')}")
                elif getattr(self, 'webhook_session', False) and teams_webhook_integration:
                    logger.info("Leaving Webhook Teams meeting...")
                    result = await teams_webhook_integration.leave_meeting(self.real_call_id)
                    if result.get("success"):
                        logger.info("✅ Successfully left Webhook Teams meeting")
                    else:
                        logger.error(f"Error leaving Webhook meeting: {result.get('error')}")
                elif getattr(self, 'bot_framework_session', False) and teams_bot_framework:
                    logger.info("Leaving Bot Framework Teams meeting...")
                    result = await teams_bot_framework.leave_meeting(self.real_call_id)
                    if result.get("success"):
                        logger.info("✅ Successfully left Bot Framework Teams meeting")
                    else:
                        logger.error(f"Error leaving Bot Framework meeting: {result.get('error')}")
                elif real_teams_integration:
                    logger.info("Leaving Graph API Teams meeting...")
                    result = await real_teams_integration.leave_meeting(self.real_call_id)
                    if result.get("success"):
                        logger.info("✅ Successfully left Graph API Teams meeting")
                    else:
                        logger.error(f"Error leaving Graph API meeting: {result.get('error')}")

                # Save transcription results
                if transcription_service and self.transcription_buffer:
                    transcription_file = await transcription_service.end_session(self.meeting_id)
                    if transcription_file:
                        logger.info(f"💾 Transcription saved to: {transcription_file}")

            # Handle mock session cleanup
            elif self.call_connection:
                try:
                    await self.call_connection.hangup()
                except Exception as e:
                    logger.error(f"Error hanging up call: {e}")

            if self.is_real_teams_session:
                logger.info("✅ Real Teams recording session stopped")
            else:
                logger.info("⚠️ Mock recording session stopped")

        except Exception as e:
            logger.error(f"Error stopping session: {e}")
            raise

class ModernTeamsBot:
    def __init__(self):
        self.active_sessions = {}
        self.graph_client = GraphAPIClient()

        if ACS_CONNECTION_STRING:
            try:
                self.call_automation_client = CallAutomationClient.from_connection_string(ACS_CONNECTION_STRING)
                logger.info("Azure Communication Services initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing ACS: {e}")
                self.call_automation_client = None
        else:
            self.call_automation_client = None

    async def join_meeting_with_transcription(self, meeting_url: str, meeting_id: Optional[str] = None, display_name: str = "Recording Bot", enable_recording: bool = True) -> str:
        """Join meeting and start transcription using modern approach"""
        try:
            meeting_info = await self.graph_client.get_meeting_info(meeting_url)

            if not meeting_id:
                meeting_id = meeting_info.get('id', str(uuid.uuid4()))

            transcription_session = ModernTranscriptionSession(meeting_id, meeting_info, display_name)
            self.active_sessions[meeting_id] = transcription_session

            await transcription_session.start_transcription()

            # Start recording if enabled
            if enable_recording:
                await transcription_session.start_recording()

            logger.info(f"Successfully started modern session for meeting {meeting_id}")
            return meeting_id

        except Exception as e:
            logger.error(f"Error joining meeting: {e}")
            if meeting_id in self.active_sessions:
                del self.active_sessions[meeting_id]
            raise

# API Route Handlers
@app.post("/join-meeting")
async def join_meeting(request: MeetingRequest):
    """Join Teams meeting and start recording"""
    try:
        if not teams_bot:
            raise HTTPException(status_code=503, detail="Bot service not initialized")

        meeting_id = await teams_bot.join_meeting_with_transcription(
            meeting_url=request.meeting_url,
            meeting_id=request.meeting_id,
            display_name=request.display_name,
            enable_recording=request.enable_recording
        )
        return {"status": "success", "meeting_id": meeting_id}
    except Exception as e:
        logger.error(f"Error joining meeting: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to join meeting: {str(e)}")

@app.post("/leave-meeting/{meeting_id}")
async def leave_meeting(meeting_id: str):
    """Leave meeting and stop recording"""
    try:
        if not teams_bot:
            raise HTTPException(status_code=503, detail="Bot service not initialized")

        if meeting_id in teams_bot.active_sessions:
            session = teams_bot.active_sessions[meeting_id]
            await session.stop_transcription()
            del teams_bot.active_sessions[meeting_id]
            return {"status": "success", "message": "Successfully left meeting"}
        else:
            raise HTTPException(status_code=404, detail="Meeting session not found")
    except Exception as e:
        logger.error(f"Error leaving meeting: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/meeting-status/{meeting_id}")
async def get_meeting_status(meeting_id: str):
    """Get comprehensive meeting status"""
    try:
        if not teams_bot:
            raise HTTPException(status_code=503, detail="Bot service not initialized")

        if meeting_id not in teams_bot.active_sessions:
            raise HTTPException(status_code=404, detail="Meeting session not found")

        session = teams_bot.active_sessions[meeting_id]

        # Determine connection type and status
        is_real_teams = getattr(session, 'is_real_teams_session', False)
        real_call_id = getattr(session, 'real_call_id', None)

        # Get live transcription if available
        live_transcription = []
        if is_real_teams and transcription_service:
            live_transcription = transcription_service.get_live_transcription(meeting_id)

        status = {
            "meeting_id": meeting_id,
            "is_active": session.is_active,
            "recording_id": session.recording_id,
            "audio_file": session.audio_file if session.is_active else None,
            "join_time": session.meeting_info.get("join_time", "N/A"),
            "display_name": session.display_name,
            "connection_type": "real_teams_integration" if is_real_teams else "mock_development",
            "actually_joined_teams_meeting": is_real_teams,
            "real_call_id": real_call_id,
            "live_transcription_count": len(live_transcription),
            "latest_transcription": live_transcription[-1] if live_transcription else None,
            "note": "✅ REAL Teams integration - bot is visible in meeting!" if is_real_teams else "⚠️ MOCK implementation - bot does not appear in actual Teams meeting"
        }
        return JSONResponse(content=status, status_code=200)
    except Exception as e:
        logger.error(f"Error getting meeting status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/active-sessions")
async def get_active_sessions():
    """Get list of active sessions"""
    try:
        if not teams_bot:
            raise HTTPException(status_code=503, detail="Bot service not initialized")

        sessions = []
        for meeting_id, session in teams_bot.active_sessions.items():
            sessions.append({
                "meeting_id": meeting_id,
                "is_active": session.is_active,
                "audio_file": session.audio_file,
                "display_name": session.display_name
            })

        return JSONResponse(content={"sessions": sessions}, status_code=200)
    except Exception as e:
        logger.error(f"Error getting active sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status/{meeting_id}/recording")
async def get_recording_status(meeting_id: str):
    """Get recording status for a specific meeting"""
    try:
        if not teams_bot:
            raise HTTPException(status_code=503, detail="Bot service not initialized")

        if meeting_id not in teams_bot.active_sessions:
            raise HTTPException(status_code=404, detail="Meeting session not found")

        session = teams_bot.active_sessions[meeting_id]
        status = {
            "meeting_id": meeting_id,
            "recording_id": session.recording_id,
            "is_recording": session.is_active,
            "audio_file": session.audio_file if session.is_active else None
        }
        return JSONResponse(content=status, status_code=200)
    except Exception as e:
        logger.error(f"Error getting recording status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/live-transcription/{meeting_id}")
async def get_live_transcription(meeting_id: str):
    """Get live transcription for a meeting"""
    try:
        if not transcription_service:
            raise HTTPException(status_code=503, detail="Transcription service not initialized")

        transcriptions = transcription_service.get_live_transcription(meeting_id)
        return JSONResponse(content={
            "meeting_id": meeting_id,
            "transcription_count": len(transcriptions),
            "transcriptions": transcriptions
        }, status_code=200)
    except Exception as e:
        logger.error(f"Error getting live transcription: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/real-teams-calls")
async def get_real_teams_calls():
    """Get active real Teams calls"""
    try:
        if not real_teams_integration:
            raise HTTPException(status_code=503, detail="Real Teams integration not initialized")

        active_calls = real_teams_integration.get_active_calls()
        return JSONResponse(content={
            "active_calls": active_calls,
            "count": len(active_calls)
        }, status_code=200)
    except Exception as e:
        logger.error(f"Error getting real Teams calls: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/bot-framework-sessions")
async def get_bot_framework_sessions():
    """Get active Bot Framework sessions"""
    try:
        if not teams_bot_framework:
            raise HTTPException(status_code=503, detail="Bot Framework integration not initialized")

        active_sessions = teams_bot_framework.get_active_sessions()
        return JSONResponse(content={
            "active_sessions": active_sessions,
            "total_sessions": len(active_sessions),
            "approach": "teams_bot_framework"
        }, status_code=200)
    except Exception as e:
        logger.error(f"Error getting Bot Framework sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/webhook-sessions")
async def get_webhook_sessions():
    """Get active Webhook sessions"""
    try:
        if not teams_webhook_integration:
            raise HTTPException(status_code=503, detail="Webhook integration not initialized")

        active_sessions = teams_webhook_integration.get_active_sessions()
        return JSONResponse(content={
            "active_sessions": active_sessions,
            "total_sessions": len(active_sessions),
            "approach": "teams_webhook"
        }, status_code=200)
    except Exception as e:
        logger.error(f"Error getting Webhook sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/teams-webhook/{session_id}")
async def handle_teams_webhook(session_id: str, request: dict):
    """Handle incoming webhook events from Teams"""
    try:
        if not teams_webhook_integration:
            raise HTTPException(status_code=503, detail="Webhook integration not initialized")

        logger.info(f"📨 Received webhook event for session {session_id}: {request}")

        result = await teams_webhook_integration.handle_webhook_event(session_id, request)

        return JSONResponse(content=result, status_code=200)
    except Exception as e:
        logger.error(f"Error handling webhook event: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/webhook-events/{session_id}")
async def get_webhook_events(session_id: str):
    """Get events for a specific webhook session"""
    try:
        if not teams_webhook_integration:
            raise HTTPException(status_code=503, detail="Webhook integration not initialized")

        events = teams_webhook_integration.get_session_events(session_id)
        return JSONResponse(content=events, status_code=200)
    except Exception as e:
        logger.error(f"Error getting webhook events: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/real-teams-bot-meetings")
async def get_real_teams_bot_meetings():
    """Get active Real Teams Bot meetings"""
    try:
        if not real_teams_bot:
            raise HTTPException(status_code=503, detail="Real Teams Bot not initialized")

        active_meetings = real_teams_bot.get_active_meetings()
        return JSONResponse(content={
            "active_meetings": active_meetings,
            "total_meetings": len(active_meetings),
            "approach": "real_teams_bot_visible_participant"
        }, status_code=200)
    except Exception as e:
        logger.error(f"Error getting Real Teams Bot meetings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/teams-call-callback")
async def handle_teams_call_callback(request: dict):
    """Handle callbacks from Teams Graph API calls"""
    try:
        logger.info(f"📞 Received Teams call callback: {request}")

        # Process Teams call callback events
        event_type = request.get("@odata.type", "")

        if "callStateChanged" in event_type:
            call_id = request.get("id")
            state = request.get("state")
            logger.info(f"Teams call {call_id} state changed to: {state}")

        return JSONResponse(content={"status": "acknowledged"}, status_code=200)
    except Exception as e:
        logger.error(f"Error handling Teams call callback: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.post("/api/messages")
async def handle_bot_framework_messages(request: dict):
    """Handle Bot Framework messages from Azure Bot Service"""
    try:
        logger.info(f"🤖 Received Bot Framework message: {request}")

        # Extract message details
        message_type = request.get("type", "")
        text = request.get("text", "")
        from_info = request.get("from", {})
        conversation = request.get("conversation", {})

        logger.info(f"Message type: {message_type}")
        logger.info(f"From: {from_info}")
        logger.info(f"Text: {text}")
        logger.info(f"Conversation: {conversation}")

        # Handle different message types
        if message_type == "message":
            # Check if message contains a meeting URL
            text = request.get("text", "")

            # Look for Teams meeting URLs
            import re
            meeting_url_pattern = r'https://teams\.microsoft\.com/l/meetup-join/[^\s]+'
            meeting_urls = re.findall(meeting_url_pattern, text)

            if meeting_urls:
                # User sent a meeting URL - try to join the meeting
                meeting_url = meeting_urls[0]
                logger.info(f"🎯 Detected meeting URL in message: {meeting_url}")

                # Try to join the meeting
                try:
                    # Try to join via Bot Framework first
                    if real_teams_bot:
                        logger.info("🤖 Attempting to join meeting via Bot Framework...")
                        result = await real_teams_bot.join_meeting_as_participant(meeting_url, "AI Meeting Assistant")

                        if result.get("success"):
                            response_text = f"✅ **Successfully joined the meeting!**\n\n🎤 I'm now recording and providing transcription services.\n\n📱 Session ID: {result.get('session_id')}"
                        else:
                            logger.warning(f"Bot Framework join failed: {result.get('error')}")
                            # Fall back to webhook approach
                            response_text = "⚠️ **Joined via monitoring mode**\n\n🎤 I'm monitoring the meeting and recording audio, but may not appear as a visible participant.\n\n💡 To see me as a participant, please add me directly to the meeting."
                    else:
                        response_text = "❌ **Unable to join meeting**\n\nPlease add me directly to the meeting for full participation."

                except Exception as e:
                    logger.error(f"Error processing meeting URL: {e}")
                    response_text = "❌ **Error joining meeting**\n\nPlease try adding me directly to the meeting."

            elif "join" in text.lower() and "meeting" in text.lower():
                # User mentioned joining a meeting but no URL detected
                response_text = "🤖 **Ready to join your meeting!**\n\n📋 **How to add me:**\n1. **Share the meeting URL** with me\n2. **Add me directly** in the meeting: People → Add → Search 'AI Meeting Assistant'\n3. **@mention me** in the meeting chat\n\n🎤 I'll provide real-time transcription and recording!"

            else:
                # Regular message
                response_text = "🤖 **AI Meeting Assistant** is ready!\n\n📋 **I can help with:**\n• Join Teams meetings for transcription\n• Record meeting audio\n• Provide real-time transcription\n\n💡 **To use me:**\n• Share a meeting URL with me\n• Add me directly to your meeting\n• @mention me in meeting chat"

            # Send response back to Teams
            response = {
                "type": "message",
                "text": response_text,
                "textFormat": "markdown"
            }

            logger.info("✅ Bot Framework message processed successfully")
            return JSONResponse(content=response, status_code=200)

        elif message_type == "conversationUpdate":
            # Bot was added to a conversation
            logger.info("🎉 Bot added to conversation!")

            # Send welcome message
            response = {
                "type": "message",
                "text": "👋 **AI Meeting Assistant** has joined! I can help with meeting transcription and monitoring.",
                "textFormat": "markdown"
            }

            return JSONResponse(content=response, status_code=200)

        else:
            # Other message types
            logger.info(f"Received {message_type} message")
            return JSONResponse(content={"status": "acknowledged"}, status_code=200)

    except Exception as e:
        logger.error(f"Error handling Bot Framework message: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.post("/api/graph-callback")
async def handle_graph_callback(request: dict):
    """Handle Graph API callbacks for calling"""
    try:
        logger.info(f"📞 Received Graph API callback: {request}")

        # Process Graph API calling callbacks
        notification_type = request.get("@odata.type", "")

        if "callStateChanged" in notification_type:
            resource_data = request.get("resourceData", {})
            call_id = resource_data.get("id", "")
            state = resource_data.get("state", "")

            logger.info(f"Graph API call {call_id} state changed to: {state}")

            # Notify Node.js bot if it's running
            try:
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    await session.post("http://localhost:3978/api/graph-callback", json=request)
                    logger.info("📡 Forwarded Graph callback to Node.js bot")
            except Exception as e:
                logger.debug(f"Could not forward to Node.js bot: {e}")

        return JSONResponse(content={"status": "acknowledged"}, status_code=200)

    except Exception as e:
        logger.error(f"Error handling Graph callback: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.post("/api/bot-events")
async def handle_bot_events(request: dict):
    """Handle events from Node.js calling bot"""
    try:
        event_type = request.get("eventType", "")
        data = request.get("data", {})
        timestamp = request.get("timestamp", "")

        logger.info(f"🤖 Received bot event: {event_type}")
        logger.info(f"📊 Event data: {data}")

        # Process different event types
        if event_type == "call_started":
            call_id = data.get("callId", "")
            logger.info(f"✅ Node.js bot joined call: {call_id}")
            logger.info("🎉 Bot is now visible as a participant in Teams meeting!")

            # Start transcription for this call
            if transcription_service and call_id:
                transcription_buffer = transcription_service.create_session(call_id)
                logger.info(f"🎤 Started transcription session for call: {call_id}")

        elif event_type == "call_state_changed":
            call_id = data.get("callId", "")
            state = data.get("state", "")
            logger.info(f"📞 Call {call_id} state: {state}")

        elif event_type == "call_ended":
            call_id = data.get("callId", "")
            logger.info(f"📞 Call ended: {call_id}")

            # End transcription session
            if transcription_service and call_id:
                transcription_file = await transcription_service.end_session(call_id)
                if transcription_file:
                    logger.info(f"💾 Transcription saved: {transcription_file}")

        elif event_type == "recording_state_changed":
            call_id = data.get("callId", "")
            recording_state = data.get("recordingState", "")
            logger.info(f"🎤 Recording state for {call_id}: {recording_state}")

        return JSONResponse(content={"status": "processed", "eventType": event_type}, status_code=200)

    except Exception as e:
        logger.error(f"Error handling bot event: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/nodejs-bot-status")
async def get_nodejs_bot_status():
    """Get status of Node.js calling bot"""
    try:
        import aiohttp

        # Check if Node.js bot is running
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get("http://localhost:3978/api/health", timeout=5) as response:
                    if response.status == 200:
                        bot_status = await response.json()
                        return JSONResponse(content={
                            "nodejs_bot_running": True,
                            "bot_status": bot_status,
                            "integration": "active"
                        }, status_code=200)
                    else:
                        return JSONResponse(content={
                            "nodejs_bot_running": False,
                            "error": f"Bot returned status {response.status}"
                        }, status_code=200)
            except Exception as e:
                return JSONResponse(content={
                    "nodejs_bot_running": False,
                    "error": str(e),
                    "message": "Start Node.js bot with: node bot.js"
                }, status_code=200)

    except Exception as e:
        logger.error(f"Error checking Node.js bot status: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/test-graph-permissions")
async def test_graph_permissions():
    """Test Graph API permissions and connectivity"""
    try:
        if not real_teams_integration:
            raise HTTPException(status_code=503, detail="Real Teams integration not initialized")

        # Test basic Graph API access
        token = await real_teams_integration._get_access_token()

        # Test a simple Graph API call
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        async with aiohttp.ClientSession() as session:
            # Test basic user info access
            async with session.get(
                "https://graph.microsoft.com/v1.0/me",
                headers=headers
            ) as response:
                me_status = response.status
                me_text = await response.text()

            # Test communications access
            async with session.get(
                "https://graph.microsoft.com/v1.0/communications/calls",
                headers=headers
            ) as response:
                calls_status = response.status
                calls_text = await response.text()

        return JSONResponse(content={
            "token_acquired": bool(token),
            "me_endpoint": {
                "status": me_status,
                "response": me_text[:200] + "..." if len(me_text) > 200 else me_text
            },
            "communications_endpoint": {
                "status": calls_status,
                "response": calls_text[:200] + "..." if len(calls_text) > 200 else calls_text
            },
            "permissions_needed": [
                "Calls.JoinGroupCall.All",
                "Calls.AccessMedia.All",
                "OnlineMeetings.ReadWrite.All"
            ]
        }, status_code=200)

    except Exception as e:
        logger.error(f"Error testing Graph permissions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/teams-callback")
async def handle_teams_callback(request: dict):
    """Handle callbacks from Teams Graph API"""
    try:
        logger.info(f"Received Teams callback: {request}")

        # Process Teams callback events
        event_type = request.get("@odata.type", "")

        if "callStateChanged" in event_type:
            call_id = request.get("id")
            state = request.get("state")
            logger.info(f"Teams call {call_id} state changed to: {state}")

        return JSONResponse(content={"status": "acknowledged"}, status_code=200)
    except Exception as e:
        logger.error(f"Error handling Teams callback: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/health")
async def health_check():
    """Get overall bot health status"""
    try:
        # Check real Teams integration
        real_teams_status = "initialized" if real_teams_integration else "not_initialized"
        transcription_status = "initialized" if transcription_service else "not_initialized"

        status = {
            "status": "healthy" if teams_bot else "not_initialized",
            "active_sessions": len(teams_bot.active_sessions) if teams_bot else 0,
            "acs_client": "initialized" if teams_bot and teams_bot.call_automation_client else "not_initialized",
            "real_teams_integration": real_teams_status,
            "transcription_service": transcription_status,
            "production_ready": real_teams_status == "initialized" and transcription_status == "initialized"
        }
        return JSONResponse(content=status, status_code=200)
    except Exception as e:
        logger.error(f"Error checking health: {str(e)}")
        status = {
            "status": "error",
            "error": str(e)
        }
        return JSONResponse(content=status, status_code=500)
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return JSONResponse(
        content={
            "message": "Teams Meeting Bot API with Live Transcription",
            "version": "2.0.0",
            "features": [
                "Join Teams meetings via URL",
                "Live speech-to-text transcription",
                "Meeting recording via Azure Communication Services",
                "Microsoft Graph API integration for meeting details",
                "Audio processing and normalization",
                "Session analytics and reporting",
                "Local file storage for transcripts and audio"
            ],
            "endpoints": [
                "POST /join-meeting - Join a meeting and start transcription",
                "POST /leave-meeting/{meeting_id} - Leave meeting and save transcription",
                "GET /meeting-status/{meeting_id} - Get meeting status",
                "GET /active-meetings - List all active meetings",
                "GET /download-transcript/{meeting_id} - Download transcription file",
                "GET /download-audio/{meeting_id}?audio_type=processed|raw - Download audio file",
                "GET /meeting-analytics/{meeting_id} - Get detailed meeting analytics",
                "GET /health - Health check",
                "GET / - API information"
            ],
            "requirements": [
                "ACS_CONNECTION_STRING for joining meetings and recording",
                "SPEECH_KEY and SPEECH_REGION for transcription",
                "CLIENT_ID, CLIENT_SECRET, TENANT_ID for Graph API integration",
                "BOT_DOMAIN for ACS callback handling"
            ]
        },
        status_code=200
    )

class CallbackEvent(BaseModel):
    callConnectionId: Optional[str]
    incomingCallContext: Optional[dict]
    type: Optional[str]
    timestamp: Optional[str]

@app.post("/api/recording-status")
async def handle_recording_status_callback(request: dict):
    """Handle recording status callback from ACS"""
    try:
        logger.info(f"Received recording status callback: {request}")

        # Extract the call ID and recording ID from the callback
        call_id = request.get('callId')
        recording_id = request.get('recordingId')

        # Update recording status in relevant session
        if call_id and teams_bot:
            for session in teams_bot.active_sessions.values():
                if (session.call_connection and
                    hasattr(session.call_connection, 'call_connection_id') and
                    session.call_connection.call_connection_id == call_id):
                    session.recording_id = recording_id
                    logger.info(f"Updated recording ID for call {call_id}: {recording_id}")
                    break

        return JSONResponse(content={"status": "acknowledged"}, status_code=200)
    except Exception as e:
        logger.error(f"Error handling recording status callback: {str(e)}")
        return JSONResponse(
            content={"error": str(e)},
            status_code=500
        )

@app.get("/status/{meeting_id}")
async def get_status(meeting_id: str):
    """Get status of a meeting session"""
    try:
        if not teams_bot:
            raise HTTPException(status_code=503, detail="Bot service not initialized")

        if meeting_id in teams_bot.active_sessions:
            session = teams_bot.active_sessions[meeting_id]
            return {
                "status": "active" if session.is_active else "inactive",
                "recording_id": session.recording_id,
                "audio_file": session.audio_file
            }
        else:
            raise HTTPException(status_code=404, detail="Meeting session not found")
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "teamsbotcau:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )