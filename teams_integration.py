"""
Real Microsoft Teams Integration using Graph API Cloud Communications
This module provides actual Teams meeting joining capabilities
"""

import os
import json
import asyncio
import logging
import websockets
import threading
from typing import Optional, Dict, Any, Callable
from datetime import datetime
import uuid

import aiohttp
import requests

logger = logging.getLogger(__name__)

try:
    from azure.identity import ClientSecretCredential
    AZURE_IDENTITY_AVAILABLE = True
except ImportError:
    AZURE_IDENTITY_AVAILABLE = False
    logger.warning("Azure Identity not available")

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    logger.warning("PyAudio not available")

try:
    import wave
    WAVE_AVAILABLE = True
except ImportError:
    WAVE_AVAILABLE = False
    logger.warning("Wave module not available")

class TeamsAudioStreamer:
    """Handles real-time audio streaming from Teams meetings"""

    def __init__(self, meeting_id: str, on_audio_data: Callable[[bytes], None]):
        self.meeting_id = meeting_id
        self.on_audio_data = on_audio_data
        self.is_streaming = False
        self.websocket = None
        self.audio_thread = None

        # Audio configuration
        self.sample_rate = 16000
        self.channels = 1
        self.chunk_size = 1024
        self.format = pyaudio.paInt16

        self.pyaudio_instance = pyaudio.PyAudio()

    async def start_streaming(self, media_url: str):
        """Start streaming audio from Teams meeting"""
        try:
            logger.info(f"Starting audio stream for meeting {self.meeting_id}")
            self.is_streaming = True

            # Connect to Teams media websocket
            self.websocket = await websockets.connect(
                media_url,
                extra_headers={
                    "User-Agent": "TeamsBot/1.0"
                }
            )

            # Start audio processing thread
            self.audio_thread = threading.Thread(target=self._process_audio_stream)
            self.audio_thread.start()

            logger.info("Audio streaming started successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start audio streaming: {e}")
            return False

    def _process_audio_stream(self):
        """Process incoming audio stream in separate thread"""
        try:
            while self.is_streaming and self.websocket:
                # Receive audio data from websocket
                audio_data = asyncio.run(self.websocket.recv())

                if audio_data and self.on_audio_data:
                    # Convert to bytes if needed
                    if isinstance(audio_data, str):
                        audio_data = audio_data.encode()

                    # Call the callback with audio data
                    self.on_audio_data(audio_data)

        except Exception as e:
            logger.error(f"Error in audio stream processing: {e}")
        finally:
            self.is_streaming = False

    async def stop_streaming(self):
        """Stop audio streaming"""
        try:
            self.is_streaming = False

            if self.websocket:
                await self.websocket.close()
                self.websocket = None

            if self.audio_thread and self.audio_thread.is_alive():
                self.audio_thread.join(timeout=5)

            logger.info("Audio streaming stopped")

        except Exception as e:
            logger.error(f"Error stopping audio stream: {e}")

class RealTeamsIntegration:
    """Real Microsoft Teams integration using Graph API Cloud Communications"""

    def __init__(self):
        self.client_id = os.getenv("CLIENT_ID")
        self.client_secret = os.getenv("CLIENT_SECRET")
        self.tenant_id = os.getenv("TENANT_ID")
        self.bot_domain = os.getenv("BOT_DOMAIN")

        # Initialize Azure credentials if available
        if AZURE_IDENTITY_AVAILABLE:
            self.credential = ClientSecretCredential(
                tenant_id=self.tenant_id,
                client_id=self.client_id,
                client_secret=self.client_secret
            )
        else:
            self.credential = None
            logger.warning("Azure Identity not available - real Teams integration disabled")

        # We'll use direct HTTP requests instead of Graph SDK for better compatibility

        self.active_calls = {}
        self.audio_streamers = {}

    async def join_teams_meeting(self, meeting_url: str, display_name: str = "AI Assistant") -> Dict[str, Any]:
        """Join a Teams meeting using Graph API Cloud Communications"""
        try:
            if not self.credential:
                return {
                    "success": False,
                    "error": "Azure Identity not available - cannot join Teams meeting"
                }

            logger.info(f"Joining Teams meeting: {meeting_url}")

            # Extract meeting info from URL (including dynamic tenant)
            meeting_info = self._extract_meeting_info(meeting_url)

            logger.info(f"Meeting info extracted: {meeting_info}")

            # Determine the approach based on tenant scenario
            is_cross_tenant = meeting_info.get("is_cross_tenant", False)
            meeting_tenant_id = meeting_info.get("meeting_tenant_id", self.tenant_id)

            if is_cross_tenant:
                logger.info(f"🌐 Cross-tenant meeting detected. Bot tenant: {self.tenant_id}, Meeting tenant: {meeting_tenant_id}")
                logger.info("Using guest access approach...")

                # Use tokenMeetingInfo for guest access to cross-tenant meetings
                call_request = {
                    "@odata.type": "#microsoft.graph.call",
                    "callbackUri": f"{self.bot_domain}/api/teams-callback",
                    "targets": [{
                        "@odata.type": "#microsoft.graph.invitationParticipantInfo",
                        "identity": {
                            "@odata.type": "#microsoft.graph.identitySet",
                            "application": {
                                "@odata.type": "#microsoft.graph.identity",
                                "id": self.client_id,
                                "displayName": display_name
                            }
                        }
                    }],
                    "requestedModalities": ["audio"],
                    "chatInfo": {
                        "@odata.type": "#microsoft.graph.chatInfo",
                        "threadId": meeting_info.get("thread_id")
                    },
                    "meetingInfo": {
                        "@odata.type": "#microsoft.graph.tokenMeetingInfo",
                        "token": meeting_url  # Use meeting URL as token for guest access
                    },
                    "source": {
                        "@odata.type": "#microsoft.graph.participantInfo",
                        "identity": {
                            "@odata.type": "#microsoft.graph.identitySet",
                            "application": {
                                "@odata.type": "#microsoft.graph.identity",
                                "id": self.client_id,
                                "displayName": display_name
                            }
                        }
                    }
                }
            else:
                logger.info(f"🏢 Same-tenant meeting detected. Using organizer approach...")

                # Use organizerMeetingInfo for same-tenant meetings
                call_request = {
                    "@odata.type": "#microsoft.graph.call",
                    "callbackUri": f"{self.bot_domain}/api/teams-callback",
                    "targets": [{
                        "@odata.type": "#microsoft.graph.invitationParticipantInfo",
                        "identity": {
                            "@odata.type": "#microsoft.graph.identitySet",
                            "application": {
                                "@odata.type": "#microsoft.graph.identity",
                                "id": self.client_id,
                                "displayName": display_name
                            }
                        }
                    }],
                    "requestedModalities": ["audio"],
                    "chatInfo": {
                        "@odata.type": "#microsoft.graph.chatInfo",
                        "threadId": meeting_info.get("thread_id")
                    },
                    "meetingInfo": {
                        "@odata.type": "#microsoft.graph.organizerMeetingInfo",
                        "organizer": {
                            "@odata.type": "#microsoft.graph.identitySet",
                            "user": {
                                "@odata.type": "#microsoft.graph.identity",
                                "id": meeting_info.get("organizer_id", self.client_id),
                                "displayName": "Meeting Organizer"
                            }
                        }
                    },
                    "source": {
                        "@odata.type": "#microsoft.graph.participantInfo",
                        "identity": {
                            "@odata.type": "#microsoft.graph.identitySet",
                            "application": {
                                "@odata.type": "#microsoft.graph.identity",
                                "id": self.client_id,
                                "displayName": display_name
                            }
                        }
                    }
                }

            # Make Graph API call to join meeting
            # Use appropriate token based on tenant scenario
            access_token = await self._get_access_token(meeting_tenant_id if is_cross_tenant else None)
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # Use tenant-specific endpoint to avoid tenant mismatch
            graph_endpoint = f"https://graph.microsoft.com/v1.0/communications/calls"

            logger.info(f"Making Graph API call to: {graph_endpoint}")
            logger.info(f"Call request payload: {json.dumps(call_request, indent=2)}")

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    graph_endpoint,
                    headers=headers,
                    json=call_request
                ) as response:

                    response_text = await response.text()
                    logger.info(f"Graph API response: Status {response.status}, Body: {response_text}")

                    if response.status == 201:
                        call_data = await response.json() if response_text else {}
                        call_id = call_data.get("id", f"call_{uuid.uuid4()}")

                        # Store call information
                        self.active_calls[call_id] = {
                            "meeting_url": meeting_url,
                            "call_data": call_data,
                            "joined_at": datetime.now(),
                            "display_name": display_name
                        }

                        logger.info(f"✅ Successfully joined Teams meeting! Call ID: {call_id}")
                        logger.info("🎉 Bot should now be visible in the Teams meeting!")

                        # Setup audio streaming
                        await self._setup_audio_streaming(call_id, call_data)

                        return {
                            "success": True,
                            "call_id": call_id,
                            "message": "Successfully joined Teams meeting",
                            "call_data": call_data
                        }
                    elif response.status == 403:
                        logger.error(f"❌ Permission denied (403). Trying alternative guest access approach...")

                        # Try alternative approach for guest access
                        if not is_cross_tenant:
                            logger.info("🔄 Retrying with guest access approach...")
                            return await self._try_guest_access_approach(meeting_url, display_name, meeting_info)

                        logger.error("❌ All approaches failed. This usually means:")
                        logger.error("1. Missing Graph API permissions: Calls.JoinGroupCall.All, Calls.AccessMedia.All")
                        logger.error("2. App not configured for Teams Cloud Communications")
                        logger.error("3. Tenant restrictions on external apps")
                        logger.error("4. Meeting doesn't allow external participants")
                        return {
                            "success": False,
                            "error": f"Permission denied - check Azure app permissions and meeting settings",
                            "details": response_text
                        }
                    else:
                        logger.error(f"❌ Failed to join meeting. Status: {response.status}")
                        logger.error(f"Response: {response_text}")
                        return {
                            "success": False,
                            "error": f"Graph API error: {response.status}",
                            "details": response_text
                        }

        except Exception as e:
            logger.error(f"Error joining Teams meeting: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _try_guest_access_approach(self, meeting_url: str, display_name: str, meeting_info: Dict[str, str]) -> Dict[str, Any]:
        """Try alternative guest access approach for joining meetings"""
        try:
            logger.info("🔄 Attempting guest access approach...")

            # Simplified guest access call request
            guest_call_request = {
                "@odata.type": "#microsoft.graph.call",
                "callbackUri": f"{self.bot_domain}/api/teams-callback",
                "requestedModalities": ["audio"],
                "chatInfo": {
                    "@odata.type": "#microsoft.graph.chatInfo",
                    "threadId": meeting_info.get("thread_id")
                },
                "meetingInfo": {
                    "@odata.type": "#microsoft.graph.tokenMeetingInfo",
                    "token": meeting_url
                },
                "source": {
                    "@odata.type": "#microsoft.graph.participantInfo",
                    "identity": {
                        "@odata.type": "#microsoft.graph.identitySet",
                        "application": {
                            "@odata.type": "#microsoft.graph.identity",
                            "id": self.client_id,
                            "displayName": display_name
                        }
                    }
                }
            }

            headers = {
                "Authorization": f"Bearer {await self._get_access_token()}",
                "Content-Type": "application/json"
            }

            logger.info(f"Guest access call request: {json.dumps(guest_call_request, indent=2)}")

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "https://graph.microsoft.com/v1.0/communications/calls",
                    headers=headers,
                    json=guest_call_request
                ) as response:

                    response_text = await response.text()
                    logger.info(f"Guest access response: Status {response.status}, Body: {response_text}")

                    if response.status == 201:
                        call_data = await response.json() if response_text else {}
                        call_id = call_data.get("id", f"guest_call_{uuid.uuid4()}")

                        self.active_calls[call_id] = {
                            "meeting_url": meeting_url,
                            "call_data": call_data,
                            "joined_at": datetime.now(),
                            "display_name": display_name,
                            "access_type": "guest"
                        }

                        logger.info(f"✅ Successfully joined Teams meeting via guest access! Call ID: {call_id}")
                        logger.info("🎉 Bot should now be visible in the Teams meeting!")

                        await self._setup_audio_streaming(call_id, call_data)

                        return {
                            "success": True,
                            "call_id": call_id,
                            "message": "Successfully joined Teams meeting via guest access",
                            "call_data": call_data,
                            "access_type": "guest"
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"Guest access failed: {response.status}",
                            "details": response_text
                        }

        except Exception as e:
            logger.error(f"Error in guest access approach: {e}")
            return {
                "success": False,
                "error": f"Guest access error: {str(e)}"
            }

    async def _setup_audio_streaming(self, call_id: str, call_data: Dict[str, Any]):
        """Setup audio streaming for the call"""
        try:
            # Get media URL from call data
            media_url = call_data.get("mediaConfig", {}).get("mediaUrl")

            if not media_url:
                logger.warning("No media URL found in call data")
                return

            # Create audio streamer
            def on_audio_data(audio_data: bytes):
                # This will be called for each audio chunk
                self._process_audio_chunk(call_id, audio_data)

            streamer = TeamsAudioStreamer(call_id, on_audio_data)
            self.audio_streamers[call_id] = streamer

            # Start streaming
            success = await streamer.start_streaming(media_url)
            if success:
                logger.info(f"Audio streaming setup complete for call {call_id}")
            else:
                logger.error(f"Failed to setup audio streaming for call {call_id}")

        except Exception as e:
            logger.error(f"Error setting up audio streaming: {e}")

    def _process_audio_chunk(self, call_id: str, audio_data: bytes):
        """Process each audio chunk from the stream"""
        try:
            # Save to file (you can modify this to stream to your backend)
            if call_id in self.active_calls:
                call_info = self.active_calls[call_id]

                # Create audio file if not exists
                if "audio_file" not in call_info:
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    audio_filename = f"teams_audio_{call_id}_{timestamp}.wav"
                    call_info["audio_file"] = audio_filename
                    call_info["audio_frames"] = []

                # Append audio data
                call_info["audio_frames"].append(audio_data)

                # Also send to transcription service (implement this)
                asyncio.create_task(self._send_to_transcription(call_id, audio_data))

        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")

    async def _send_to_transcription(self, call_id: str, audio_data: bytes):
        """Send audio data to transcription service"""
        try:
            # This is where you'd send audio to your transcription backend
            # For now, we'll just log that we received audio
            logger.debug(f"Received {len(audio_data)} bytes of audio for call {call_id}")

            # TODO: Implement real-time transcription
            # - Send to Azure Speech Services
            # - Send to OpenAI Whisper
            # - Send to Deepgram

        except Exception as e:
            logger.error(f"Error sending to transcription: {e}")

    async def leave_meeting(self, call_id: str) -> Dict[str, Any]:
        """Leave a Teams meeting"""
        try:
            if call_id not in self.active_calls:
                return {"success": False, "error": "Call not found"}

            # Stop audio streaming
            if call_id in self.audio_streamers:
                await self.audio_streamers[call_id].stop_streaming()
                del self.audio_streamers[call_id]

            # End the call via Graph API
            headers = {
                "Authorization": f"Bearer {await self._get_access_token()}",
                "Content-Type": "application/json"
            }

            async with aiohttp.ClientSession() as session:
                async with session.delete(
                    f"https://graph.microsoft.com/v1.0/communications/calls/{call_id}",
                    headers=headers
                ) as response:

                    if response.status in [200, 204]:
                        # Save final audio file
                        await self._save_final_audio(call_id)

                        # Clean up
                        del self.active_calls[call_id]

                        logger.info(f"Successfully left meeting. Call ID: {call_id}")
                        return {"success": True, "message": "Successfully left meeting"}
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to leave meeting: {error_text}")
                        return {"success": False, "error": error_text}

        except Exception as e:
            logger.error(f"Error leaving meeting: {e}")
            return {"success": False, "error": str(e)}

    async def _save_final_audio(self, call_id: str):
        """Save the final audio file"""
        try:
            if call_id in self.active_calls:
                call_info = self.active_calls[call_id]
                audio_frames = call_info.get("audio_frames", [])
                audio_file = call_info.get("audio_file")

                if audio_frames and audio_file:
                    # Save as WAV file
                    with wave.open(audio_file, 'wb') as wf:
                        wf.setnchannels(1)  # Mono
                        wf.setsampwidth(2)  # 16-bit
                        wf.setframerate(16000)  # 16kHz
                        wf.writeframes(b''.join(audio_frames))

                    logger.info(f"Saved audio file: {audio_file}")

        except Exception as e:
            logger.error(f"Error saving audio file: {e}")

    def _extract_meeting_info(self, meeting_url: str) -> Dict[str, str]:
        """Extract meeting information from Teams URL including dynamic tenant"""
        try:
            # Extract thread ID from URL
            import re
            from urllib.parse import unquote, parse_qs, urlparse
            import json

            # Pattern to extract meeting thread ID
            thread_pattern = r'meetup-join/([^/?]+)'
            match = re.search(thread_pattern, meeting_url)

            if match:
                thread_id = unquote(match.group(1))

                # Extract tenant ID and organizer ID from context
                meeting_tenant_id = self.tenant_id  # Default to bot's tenant
                organizer_id = ""

                try:
                    parsed_url = urlparse(meeting_url)
                    if parsed_url.query:
                        query_params = parse_qs(parsed_url.query)
                        context = query_params.get('context', [''])[0]
                        if context:
                            context_decoded = unquote(context)
                            logger.info(f"Meeting context: {context_decoded}")

                            # Parse the context JSON
                            try:
                                context_json = json.loads(context_decoded)

                                # Extract tenant ID from meeting context
                                if 'Tid' in context_json:
                                    meeting_tenant_id = context_json['Tid']
                                    logger.info(f"Extracted meeting tenant ID: {meeting_tenant_id}")

                                # Extract organizer ID
                                if 'Oid' in context_json:
                                    organizer_id = context_json['Oid']
                                    logger.info(f"Extracted organizer ID: {organizer_id}")

                            except json.JSONDecodeError:
                                # Fallback to regex if JSON parsing fails
                                tid_match = re.search(r'"Tid":"([^"]+)"', context_decoded)
                                if tid_match:
                                    meeting_tenant_id = tid_match.group(1)
                                    logger.info(f"Extracted meeting tenant ID (regex): {meeting_tenant_id}")

                                oid_match = re.search(r'"Oid":"([^"]+)"', context_decoded)
                                if oid_match:
                                    organizer_id = oid_match.group(1)
                                    logger.info(f"Extracted organizer ID (regex): {organizer_id}")

                except Exception as e:
                    logger.warning(f"Could not extract tenant/organizer info: {e}")

                return {
                    "thread_id": thread_id,
                    "organizer_id": organizer_id,
                    "meeting_url": meeting_url,
                    "meeting_tenant_id": meeting_tenant_id,
                    "is_cross_tenant": meeting_tenant_id != self.tenant_id
                }

            return {
                "thread_id": str(uuid.uuid4()),
                "meeting_url": meeting_url,
                "meeting_tenant_id": self.tenant_id,
                "is_cross_tenant": False
            }

        except Exception as e:
            logger.error(f"Error extracting meeting info: {e}")
            return {
                "thread_id": str(uuid.uuid4()),
                "meeting_url": meeting_url,
                "meeting_tenant_id": self.tenant_id,
                "is_cross_tenant": False
            }

    async def _get_access_token(self, tenant_id: str = None) -> str:
        """Get access token for Graph API, optionally for a specific tenant"""
        try:
            # Use the provided tenant_id or default to bot's tenant
            target_tenant = tenant_id or self.tenant_id

            if target_tenant != self.tenant_id:
                logger.info(f"Getting cross-tenant token for tenant: {target_tenant}")
                # For cross-tenant scenarios, we still use our own credentials
                # but the token will be used for guest access

            token = self.credential.get_token("https://graph.microsoft.com/.default")
            return token.token
        except Exception as e:
            logger.error(f"Error getting access token: {e}")
            raise

    def get_active_calls(self) -> Dict[str, Any]:
        """Get information about active calls"""
        return {
            call_id: {
                "meeting_url": info["meeting_url"],
                "joined_at": info["joined_at"].isoformat(),
                "display_name": info["display_name"],
                "has_audio_stream": call_id in self.audio_streamers
            }
            for call_id, info in self.active_calls.items()
        }
