import os
from fastapi import FastAPI, Request
from dotenv import load_dotenv
from azure.communication.callautomation import (
    CallAutomationClient,
    CallInvite,
    TeamsMeetingLinkLocator
)

load_dotenv()

# Load configs
ACS_CONNECTION_STRING = os.getenv("ACS_CONNECTION_STRING")
TEAMS_MEETING_LINK = "https://teams.microsoft.com/l/meetup-join/19%3ameeting_MmFlODEzZmEtNjVlMC00YTI5LWFhM2EtZGRiOWQ2NjRkNDc3%40thread.v2/0?context=%7b%22Tid%22%3a%229750f5c0-d037-4115-a5b2-b25c5994ed60%22%2c%22Oid%22%3a%22ede12e40-6b7d-4a77-ae47-d04c79584154%22%7d"
PUBLIC_CALLBACK_URL = "https://689c-20-169-212-255.ngrok-free.app/api/callbacks"

# Initialize ACS Call Automation Client
call_automation_client = CallAutomationClient.from_connection_string(ACS_CONNECTION_STRING)

# FastAPI app
app = FastAPI()

@app.get("/")
async def root():
    return {"status": "Bot is running!"}

@app.post("/api/callbacks")
async def handle_callbacks(request: Request):
    data = await request.json()
    print("\n📞 Received callback event:")
    print(data)
    return {"status": "OK"}

@app.post("/start_call")
async def start_call():
    print(f"Dialing into Teams: {TEAMS_MEETING_LINK}")

    # ✅ Correct: Use TeamsMeetingLinkLocator
    teams_link_locator = TeamsMeetingLinkLocator(meeting_link=TEAMS_MEETING_LINK)

    call_invite = CallInvite(
        target=teams_link_locator
    )

    call_connection = call_automation_client.create_call(
        call_invite=call_invite,
        callback_url=PUBLIC_CALLBACK_URL
    )

    print(f"Started call with ID: {call_connection.call_connection_id}")

    return {"call_connection_id": call_connection.call_connection_id}
