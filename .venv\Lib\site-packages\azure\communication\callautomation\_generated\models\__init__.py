# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
# pylint: disable=wrong-import-position

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._patch import *  # pylint: disable=unused-wildcard-import


from ._models import (  # type: ignore
    AddParticipantFailed,
    AddParticipantRequest,
    AddParticipantResponse,
    AddPart<PERSON>pantSucceeded,
    AnswerCallRequest,
    AnswerF<PERSON>,
    <PERSON><PERSON>on<PERSON><PERSON>,
    <PERSON><PERSON>on<PERSON><PERSON><PERSON>roper<PERSON>,
    CallDisconnected,
    CallIntelligenceOptions,
    CallLocator,
    CallParticipant,
    CallTransferAccepted,
    CallTransferFailed,
    CancelAddParticipantFailed,
    CancelAddParticipantRequest,
    CancelAddParticipantResponse,
    CancelAddParticipantSucceeded,
    ChannelAffinity,
    Choice,
    ChoiceResult,
    CommunicationError,
    CommunicationErrorResponse,
    CommunicationIdentifierModel,
    CommunicationUserIdentifierModel,
    ConnectFailed,
    ConnectRequest,
    ContinuousDtmfRecognitionRequest,
    ContinuousDtmfRecognitionStopped,
    ContinuousDtmfRecognitionToneFailed,
    ContinuousDtmfRecognitionToneReceived,
    CreateCallFailed,
    CreateCallRequest,
    CustomCallingContext,
    DtmfOptions,
    DtmfResult,
    ExternalStorage,
    FileSource,
    HoldFailed,
    HoldRequest,
    MediaStreamingFailed,
    MediaStreamingOptions,
    MediaStreamingStarted,
    MediaStreamingStopped,
    MediaStreamingSubscription,
    MediaStreamingUpdate,
    MicrosoftTeamsAppIdentifierModel,
    MicrosoftTeamsUserIdentifierModel,
    MuteParticipantsRequest,
    MuteParticipantsResult,
    ParticipantsUpdated,
    PhoneNumberIdentifierModel,
    PlayCanceled,
    PlayCompleted,
    PlayFailed,
    PlayOptions,
    PlayRequest,
    PlaySource,
    PlayStarted,
    RecognizeCanceled,
    RecognizeCompleted,
    RecognizeFailed,
    RecognizeOptions,
    RecognizeRequest,
    RecordingStateChanged,
    RecordingStateResponse,
    RedirectCallRequest,
    RejectCallRequest,
    RemoveParticipantFailed,
    RemoveParticipantRequest,
    RemoveParticipantResponse,
    RemoveParticipantSucceeded,
    ResultInformation,
    SendDtmfTonesCompleted,
    SendDtmfTonesFailed,
    SendDtmfTonesRequest,
    SendDtmfTonesResult,
    SpeechOptions,
    SpeechResult,
    SsmlSource,
    StartCallRecordingRequest,
    StartMediaStreamingRequest,
    StartTranscriptionRequest,
    StopMediaStreamingRequest,
    StopTranscriptionRequest,
    TextSource,
    TranscriptionFailed,
    TranscriptionOptions,
    TranscriptionStarted,
    TranscriptionStopped,
    TranscriptionSubscription,
    TranscriptionUpdate,
    TranscriptionUpdated,
    TransferCallResponse,
    TransferToParticipantRequest,
    UnholdRequest,
    UpdateTranscriptionRequest,
    WebSocketMediaStreamingOptions,
    WebSocketTranscriptionOptions,
)

from ._enums import (  # type: ignore
    AudioFormat,
    CallConnectionState,
    CallLocatorKind,
    CallRejectReason,
    CommunicationCloudEnvironmentModel,
    CommunicationIdentifierModelKind,
    DtmfTone,
    MediaStreamingAudioChannelType,
    MediaStreamingContentType,
    MediaStreamingStatus,
    MediaStreamingStatusDetails,
    MediaStreamingSubscriptionState,
    PlaySourceType,
    RecognitionType,
    RecognizeInputType,
    RecordingChannel,
    RecordingContent,
    RecordingFormat,
    RecordingKind,
    RecordingState,
    RecordingStorageKind,
    StreamingTransportType,
    TranscriptionResultType,
    TranscriptionStatus,
    TranscriptionStatusDetails,
    TranscriptionSubscriptionState,
    VoiceKind,
)
from ._patch import __all__ as _patch_all
from ._patch import *
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "AddParticipantFailed",
    "AddParticipantRequest",
    "AddParticipantResponse",
    "AddParticipantSucceeded",
    "AnswerCallRequest",
    "AnswerFailed",
    "CallConnected",
    "CallConnectionProperties",
    "CallDisconnected",
    "CallIntelligenceOptions",
    "CallLocator",
    "CallParticipant",
    "CallTransferAccepted",
    "CallTransferFailed",
    "CancelAddParticipantFailed",
    "CancelAddParticipantRequest",
    "CancelAddParticipantResponse",
    "CancelAddParticipantSucceeded",
    "ChannelAffinity",
    "Choice",
    "ChoiceResult",
    "CommunicationError",
    "CommunicationErrorResponse",
    "CommunicationIdentifierModel",
    "CommunicationUserIdentifierModel",
    "ConnectFailed",
    "ConnectRequest",
    "ContinuousDtmfRecognitionRequest",
    "ContinuousDtmfRecognitionStopped",
    "ContinuousDtmfRecognitionToneFailed",
    "ContinuousDtmfRecognitionToneReceived",
    "CreateCallFailed",
    "CreateCallRequest",
    "CustomCallingContext",
    "DtmfOptions",
    "DtmfResult",
    "ExternalStorage",
    "FileSource",
    "HoldFailed",
    "HoldRequest",
    "MediaStreamingFailed",
    "MediaStreamingOptions",
    "MediaStreamingStarted",
    "MediaStreamingStopped",
    "MediaStreamingSubscription",
    "MediaStreamingUpdate",
    "MicrosoftTeamsAppIdentifierModel",
    "MicrosoftTeamsUserIdentifierModel",
    "MuteParticipantsRequest",
    "MuteParticipantsResult",
    "ParticipantsUpdated",
    "PhoneNumberIdentifierModel",
    "PlayCanceled",
    "PlayCompleted",
    "PlayFailed",
    "PlayOptions",
    "PlayRequest",
    "PlaySource",
    "PlayStarted",
    "RecognizeCanceled",
    "RecognizeCompleted",
    "RecognizeFailed",
    "RecognizeOptions",
    "RecognizeRequest",
    "RecordingStateChanged",
    "RecordingStateResponse",
    "RedirectCallRequest",
    "RejectCallRequest",
    "RemoveParticipantFailed",
    "RemoveParticipantRequest",
    "RemoveParticipantResponse",
    "RemoveParticipantSucceeded",
    "ResultInformation",
    "SendDtmfTonesCompleted",
    "SendDtmfTonesFailed",
    "SendDtmfTonesRequest",
    "SendDtmfTonesResult",
    "SpeechOptions",
    "SpeechResult",
    "SsmlSource",
    "StartCallRecordingRequest",
    "StartMediaStreamingRequest",
    "StartTranscriptionRequest",
    "StopMediaStreamingRequest",
    "StopTranscriptionRequest",
    "TextSource",
    "TranscriptionFailed",
    "TranscriptionOptions",
    "TranscriptionStarted",
    "TranscriptionStopped",
    "TranscriptionSubscription",
    "TranscriptionUpdate",
    "TranscriptionUpdated",
    "TransferCallResponse",
    "TransferToParticipantRequest",
    "UnholdRequest",
    "UpdateTranscriptionRequest",
    "WebSocketMediaStreamingOptions",
    "WebSocketTranscriptionOptions",
    "AudioFormat",
    "CallConnectionState",
    "CallLocatorKind",
    "CallRejectReason",
    "CommunicationCloudEnvironmentModel",
    "CommunicationIdentifierModelKind",
    "DtmfTone",
    "MediaStreamingAudioChannelType",
    "MediaStreamingContentType",
    "MediaStreamingStatus",
    "MediaStreamingStatusDetails",
    "MediaStreamingSubscriptionState",
    "PlaySourceType",
    "RecognitionType",
    "RecognizeInputType",
    "RecordingChannel",
    "RecordingContent",
    "RecordingFormat",
    "RecordingKind",
    "RecordingState",
    "RecordingStorageKind",
    "StreamingTransportType",
    "TranscriptionResultType",
    "TranscriptionStatus",
    "TranscriptionStatusDetails",
    "TranscriptionSubscriptionState",
    "VoiceKind",
]
__all__.extend([p for p in _patch_all if p not in __all__])  # pyright: ignore
_patch_sdk()
