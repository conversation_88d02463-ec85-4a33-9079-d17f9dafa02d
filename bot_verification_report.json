{"verification_timestamp": "2025-06-18T06:16:49.855916", "overall_status": "⚠️ Issues Found", "results": {"environment_variables": {"CLIENT_ID": {"status": "✅ Present", "value_length": 36, "masked_value": "4a521234..."}, "CLIENT_SECRET": {"status": "✅ Present", "value_length": 40, "masked_value": "1Vy8Q~L4..."}, "TENANT_ID": {"status": "✅ Present", "value_length": 36, "masked_value": "9750f5c0..."}, "BOT_DOMAIN": {"status": "✅ Present", "value_length": 42, "masked_value": "https://..."}}, "azure_app_registration": {"status": "✅ Valid", "token_acquired": true, "expires_in": 3599, "token_type": "Bearer"}, "graph_api_permissions": {"Application Info": "❌ Forbidden - Missing permissions", "Service Principals": "❌ Forbidden - Missing permissions", "Communications": "✅ Accessible"}, "bot_framework": {"Bot Messages": "⚠️ HTTP 404", "Graph Callback": "⚠️ HTTP 404", "Health Check": "⚠️ HTTP 404"}, "teams_integration": {}, "recommendations": ["Grant permissions for Application Info", "Grant permissions for Service Principals"], "required_permissions": {"Microsoft Graph (Application)": ["Calls.JoinGroupCall.All", "Calls.AccessMedia.All", "Calls.Initiate.All", "OnlineMeetings.ReadWrite.All", "User.Read.All", "Application.Read.All"], "Azure Bot Service": ["Bot Framework registration", "Teams channel enabled", "Calling channel enabled"], "Teams App": ["App manifest uploaded", "<PERSON><PERSON> added to Teams", "Calling permissions granted"]}}}