import os
import asyncio
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import uuid
import threading
import re
import time
import wave
import aiohttp
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.responses import JSONResponse, FileResponse
import uvicorn
from pydantic import BaseModel
from azure.communication.callautomation import CallAutomationClient
from dotenv import load_dotenv
import msal

load_dotenv()

app = FastAPI(title="Teams Meeting Bot with Modern Azure SDKs", version="2.0.0")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
CLIENT_ID = '4a521234-7111-46a9-a2fb-a6f69255dd3a'
CLIENT_SECRET = '****************************************'
TENANT_ID = os.getenv("TENANT_ID")
SPEECH_KEY = os.getenv("SPEECH_KEY")
SPEECH_REGION = os.getenv("SPEECH_REGION")
ACS_CONNECTION_STRING = os.getenv("ACS_CONNECTION_STRING", "")
USER_OBJECT_ID = os.getenv("USER_OBJECT_ID")
BOT_DOMAIN = 'https://3540-20-169-212-255.ngrok-free.app'

class AudioStreamRecorder:
    def __init__(self, filename: str, sample_rate: int = 16000, channels: int = 1):
        self.filename = filename
        self.sample_rate = sample_rate
        self.channels = channels
        self.frames = []
        self.is_recording = False
        self.audio_lock = threading.Lock()
        
    def start_recording(self):
        self.is_recording = True
        self.frames = []
        logger.info(f"Started recording audio to {self.filename}")
        
    def add_audio_data(self, audio_data: bytes):
        if self.is_recording:
            with self.audio_lock:
                self.frames.append(audio_data)
    
    def stop_recording(self):
        self.is_recording = False
        self.save_audio()
    
    def save_audio(self):
        if not self.frames:
            logger.warning("No audio frames to save")
            return
        
        try:
            with wave.open(self.filename, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(2)  # 16-bit audio
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(self.frames))
            logger.info(f"Audio saved to {self.filename}")
        except Exception as e:
            logger.error(f"Error saving audio: {e}")

class ModernTranscriptionSession:
    def __init__(self, meeting_id: str, meeting_info: Dict[str, Any], display_name: str = "Bot"):
        self.meeting_id = meeting_id
        self.meeting_info = meeting_info
        self.display_name = display_name
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.audio_file = f"audio_{meeting_id}_{timestamp}.wav"
        self.is_active = False
        self.call_automation_client = None
        self.call_connection = None
        self.audio_recorder = AudioStreamRecorder(self.audio_file)
        self.graph_client = GraphAPIClient()
        self.recording_id = None
        
    async def start_transcription(self):
        """Start the recording session"""
        try:
            # Initialize Call Automation if ACS is configured
            if ACS_CONNECTION_STRING:
                await self._setup_call_automation()
            
            self.is_active = True
            self.audio_recorder.start_recording()
            logger.info(f"Successfully started modern session for meeting {self.meeting_id}")
            
        except Exception as e:
            logger.error(f"Error starting session: {e}")
            raise
            
    async def _setup_call_automation(self):
        """Setup Call Automation client for call management"""
        try:
            if not ACS_CONNECTION_STRING:
                logger.warning("ACS not configured, skipping call automation setup")
                return
            
            self.call_automation_client = CallAutomationClient.from_connection_string(ACS_CONNECTION_STRING)
            logger.info("Call Automation client initialized")
            
            # Get meeting URL and validate it
            if not hasattr(self, 'meeting_info') or not self.meeting_info:
                raise ValueError("Meeting info not available")
            
            meeting_url = self.meeting_info.get('joinUrl') or self.meeting_info.get('join_url')
            if not meeting_url:
                raise ValueError("Meeting URL not found in meeting info")
            
            logger.info(f"Joining meeting with URL: {meeting_url}")
            
            # Join the Teams meeting
            call_connection = self.call_automation_client.create_call(
                targets=[{
                    "teamsCall": {
                        "meetingLink": meeting_url,
                        "displayName": self.display_name
                    }
                }],
                callback_url=f"{BOT_DOMAIN}/api/recording-status"
            )
            
            self.call_connection = call_connection
            logger.info(f"Joined Teams meeting {self.meeting_id} via Call Automation")
            
            # Setup audio stream handling
            self._setup_audio_handlers()
            
        except Exception as e:
            logger.error(f"Error setting up call automation: {e}")
            raise

    def _setup_audio_handlers(self):
        """Setup audio stream handlers for the call"""
        try:
            if self.call_connection:
                logger.info(f"Call connection established with ID: {self.call_connection.call_connection_id}")
                logger.info("Audio stream handlers setup completed")
        except Exception as e:
            logger.error(f"Error setting up audio handlers: {e}")
    
    def process_audio_stream(self, audio_data: bytes):
        """Process incoming audio stream data"""
        try:
            self.audio_recorder.add_audio_data(audio_data)
        except Exception as e:
            logger.error(f"Error processing audio stream: {e}")

    async def stop_transcription(self):
        """Stop the recording session"""
        try:
            self.is_active = False
            self.audio_recorder.stop_recording()
            
            if self.call_connection:
                try:
                    await self.call_connection.hangup()
                except Exception as e:
                    logger.error(f"Error hanging up call: {e}")
            
            logger.info("Recording session stopped")
            
        except Exception as e:
            logger.error(f"Error stopping session: {e}")
            raise

class GraphAPIClient:
    def __init__(self):
        self.app = msal.ConfidentialClientApplication(
            CLIENT_ID,
            authority=f"https://login.microsoftonline.com/{TENANT_ID}",
            client_credential=CLIENT_SECRET,
        )
        self.access_token = None
        self.token_expires_at = 0
    
    async def get_access_token(self):
        """Get Microsoft Graph API access token"""
        logger.info("Attempting to acquire Graph API token")
        if self.access_token and time.time() < self.token_expires_at:
            logger.info("Using cached token")
            return self.access_token
        
        try:
            result = self.app.acquire_token_silent(["https://graph.microsoft.com/.default"], account=None)
            if not result:
                logger.info("No silent token, acquiring new token")
                result = self.app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
            
            if "access_token" in result:
                self.access_token = result["access_token"]
                self.token_expires_at = time.time() + result.get("expires_in", 3600) - 300
                logger.info("Token acquired successfully")
                return self.access_token
            else:
                logger.error(f"Token acquisition failed: {result}")
                raise Exception(f"Failed to get access token: {result.get('error_description')}")
        except Exception as e:
            logger.error(f"Token acquisition error: {e}")
            raise

    async def get_meeting_info(self, meeting_url: str):
        """Extract meeting information from Teams meeting URL"""
        try:
            token = await self.get_access_token()
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Extract meeting ID from URL
            meeting_id = self.extract_meeting_id_from_url(meeting_url)
            logger.info(f"Extracted meeting ID: {meeting_id} from URL: {meeting_url}")
            
            async with aiohttp.ClientSession() as session:
                url = f"https://graph.microsoft.com/v1.0/users/{USER_OBJECT_ID}/onlineMeetings/{meeting_id}"
                logger.info(f"Sending Graph API request to: {url}")
                async with session.get(url, headers=headers) as response:
                    response_text = await response.text()
                    logger.info(f"Graph API response status: {response.status}, body: {response_text}")
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"Could not fetch meeting details: {response.status}")
                        return {"id": meeting_id, "joinUrl": meeting_url}
                        
        except Exception as e:
            logger.error(f"Error getting meeting info: {e}")
            return {"id": str(uuid.uuid4()), "joinUrl": meeting_url}
    
    def extract_meeting_id_from_url(self, meeting_url: str) -> str:
        """Extract meeting ID from Teams meeting URL"""
        patterns = [
            r'meetup-join/([^/?]+)',
            r'meeting/([^/?]+)',
            r'join/([^/?]+)',
            r'[?&]meetingID=([^&]+)',
            r'[?&]threadId=([^&]+)',
            r'/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, meeting_url, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return str(uuid.uuid4())

class MeetingRequest(BaseModel):
    meeting_url: str
    meeting_id: Optional[str] = None
    display_name: Optional[str] = "Recording Bot"
    enable_recording: Optional[bool] = True

class CallbackEvent(BaseModel):
    callConnectionId: Optional[str]
    incomingCallContext: Optional[dict]
    type: Optional[str]
    timestamp: Optional[str]

@app.post("/join-meeting")
async def join_meeting(request: MeetingRequest):
    """Join Teams meeting and start recording"""
    try:
        bot = ModernTeamsBot()
        meeting_id = await bot.join_meeting_with_transcription(
            meeting_url=request.meeting_url,
            meeting_id=request.meeting_id,
            display_name=request.display_name,
            enable_recording=request.enable_recording
        )
        return {"status": "success", "meeting_id": meeting_id}
    except Exception as e:
        logger.error(f"Error joining meeting: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to join meeting: {str(e)}")

@app.post("/api/recording-status")
async def handle_callback(event: CallbackEvent):
    """Handle callback events from ACS"""
    try:
        logger.info(f"Received callback event: {event.dict()}")
        return {"status": "success"}
    except Exception as e:
        logger.error(f"Error handling callback: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)