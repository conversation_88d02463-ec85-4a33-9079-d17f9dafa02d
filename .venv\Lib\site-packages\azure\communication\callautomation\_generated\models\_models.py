# pylint: disable=too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import Any, Dict, List, Optional, TYPE_CHECKING, Union

from .._utils import serialization as _serialization

if TYPE_CHECKING:
    from .. import models as _models


class AddParticipantFailed(_serialization.Model):
    """The failed to add participants event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar participant: Participant.
    :vartype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "participant": {"key": "participant", "type": "CommunicationIdentifierModel"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        participant: Optional["_models.CommunicationIdentifierModel"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword participant: Participant.
        :paramtype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information
        self.participant = participant


class AddParticipantRequest(_serialization.Model):
    """The request payload for adding participant to the call.

    All required parameters must be populated in order to send to server.

    :ivar source_caller_id_number: The source caller Id, a phone number, that's shown to the PSTN
     participant being invited.
     Required only when inviting a PSTN participant.
    :vartype source_caller_id_number:
     ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
    :ivar source_display_name: (Optional) The display name of the source that is associated with
     this invite operation when
     adding a PSTN participant or teams user.  Note: Will not update the display name in the roster.
    :vartype source_display_name: str
    :ivar participant_to_add: The participant to invite. Required.
    :vartype participant_to_add:
     ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar invitation_timeout_in_seconds: Gets or sets the timeout to wait for the invited
     participant to pickup.
     The maximum value of this is 180 seconds.
    :vartype invitation_timeout_in_seconds: int
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    :ivar custom_calling_context: Used by customer to send custom calling context to targets.
    :vartype custom_calling_context:
     ~azure.communication.callautomation.models.CustomCallingContext
    """

    _validation = {
        "participant_to_add": {"required": True},
        "invitation_timeout_in_seconds": {"maximum": 180, "minimum": 0},
    }

    _attribute_map = {
        "source_caller_id_number": {"key": "sourceCallerIdNumber", "type": "PhoneNumberIdentifierModel"},
        "source_display_name": {"key": "sourceDisplayName", "type": "str"},
        "participant_to_add": {"key": "participantToAdd", "type": "CommunicationIdentifierModel"},
        "invitation_timeout_in_seconds": {"key": "invitationTimeoutInSeconds", "type": "int"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
        "custom_calling_context": {"key": "customCallingContext", "type": "CustomCallingContext"},
    }

    def __init__(
        self,
        *,
        participant_to_add: "_models.CommunicationIdentifierModel",
        source_caller_id_number: Optional["_models.PhoneNumberIdentifierModel"] = None,
        source_display_name: Optional[str] = None,
        invitation_timeout_in_seconds: Optional[int] = None,
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        custom_calling_context: Optional["_models.CustomCallingContext"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword source_caller_id_number: The source caller Id, a phone number, that's shown to the
         PSTN participant being invited.
         Required only when inviting a PSTN participant.
        :paramtype source_caller_id_number:
         ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
        :keyword source_display_name: (Optional) The display name of the source that is associated with
         this invite operation when
         adding a PSTN participant or teams user.  Note: Will not update the display name in the roster.
        :paramtype source_display_name: str
        :keyword participant_to_add: The participant to invite. Required.
        :paramtype participant_to_add:
         ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword invitation_timeout_in_seconds: Gets or sets the timeout to wait for the invited
         participant to pickup.
         The maximum value of this is 180 seconds.
        :paramtype invitation_timeout_in_seconds: int
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        :keyword custom_calling_context: Used by customer to send custom calling context to targets.
        :paramtype custom_calling_context:
         ~azure.communication.callautomation.models.CustomCallingContext
        """
        super().__init__(**kwargs)
        self.source_caller_id_number = source_caller_id_number
        self.source_display_name = source_display_name
        self.participant_to_add = participant_to_add
        self.invitation_timeout_in_seconds = invitation_timeout_in_seconds
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri
        self.custom_calling_context = custom_calling_context


class AddParticipantResponse(_serialization.Model):
    """The response payload for adding participants to the call.

    :ivar participant: List of current participants in the call.
    :vartype participant: ~azure.communication.callautomation.models.CallParticipant
    :ivar operation_context: The operation context provided by client.
    :vartype operation_context: str
    :ivar invitation_id: Invitation ID used to add a participant.
    :vartype invitation_id: str
    """

    _attribute_map = {
        "participant": {"key": "participant", "type": "CallParticipant"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "invitation_id": {"key": "invitationId", "type": "str"},
    }

    def __init__(
        self,
        *,
        participant: Optional["_models.CallParticipant"] = None,
        operation_context: Optional[str] = None,
        invitation_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword participant: List of current participants in the call.
        :paramtype participant: ~azure.communication.callautomation.models.CallParticipant
        :keyword operation_context: The operation context provided by client.
        :paramtype operation_context: str
        :keyword invitation_id: Invitation ID used to add a participant.
        :paramtype invitation_id: str
        """
        super().__init__(**kwargs)
        self.participant = participant
        self.operation_context = operation_context
        self.invitation_id = invitation_id


class AddParticipantSucceeded(_serialization.Model):
    """The participants successfully added event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar participant: Participant.
    :vartype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "participant": {"key": "participant", "type": "CommunicationIdentifierModel"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        participant: Optional["_models.CommunicationIdentifierModel"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword participant: Participant.
        :paramtype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information
        self.participant = participant


class AnswerCallRequest(_serialization.Model):
    """The request payload for answering the call.

    All required parameters must be populated in order to send to server.

    :ivar incoming_call_context: The context associated with the call. Required.
    :vartype incoming_call_context: str
    :ivar callback_uri: The callback uri. Required.
    :vartype callback_uri: str
    :ivar operation_context: A customer set value used to track the answering of a call.
    :vartype operation_context: str
    :ivar call_intelligence_options: AI options for the call.
    :vartype call_intelligence_options:
     ~azure.communication.callautomation.models.CallIntelligenceOptions
    :ivar answered_by: The identifier of the call automation entity which answers the call.
    :vartype answered_by:
     ~azure.communication.callautomation.models.CommunicationUserIdentifierModel
    :ivar media_streaming_options: Media Streaming Options.
    :vartype media_streaming_options:
     ~azure.communication.callautomation.models.MediaStreamingOptions
    :ivar transcription_options: Transcription Options.
    :vartype transcription_options: ~azure.communication.callautomation.models.TranscriptionOptions
    """

    _validation = {
        "incoming_call_context": {"required": True},
        "callback_uri": {"required": True},
    }

    _attribute_map = {
        "incoming_call_context": {"key": "incomingCallContext", "type": "str"},
        "callback_uri": {"key": "callbackUri", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "call_intelligence_options": {"key": "callIntelligenceOptions", "type": "CallIntelligenceOptions"},
        "answered_by": {"key": "answeredBy", "type": "CommunicationUserIdentifierModel"},
        "media_streaming_options": {"key": "mediaStreamingOptions", "type": "MediaStreamingOptions"},
        "transcription_options": {"key": "transcriptionOptions", "type": "TranscriptionOptions"},
    }

    def __init__(
        self,
        *,
        incoming_call_context: str,
        callback_uri: str,
        operation_context: Optional[str] = None,
        call_intelligence_options: Optional["_models.CallIntelligenceOptions"] = None,
        answered_by: Optional["_models.CommunicationUserIdentifierModel"] = None,
        media_streaming_options: Optional["_models.MediaStreamingOptions"] = None,
        transcription_options: Optional["_models.TranscriptionOptions"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword incoming_call_context: The context associated with the call. Required.
        :paramtype incoming_call_context: str
        :keyword callback_uri: The callback uri. Required.
        :paramtype callback_uri: str
        :keyword operation_context: A customer set value used to track the answering of a call.
        :paramtype operation_context: str
        :keyword call_intelligence_options: AI options for the call.
        :paramtype call_intelligence_options:
         ~azure.communication.callautomation.models.CallIntelligenceOptions
        :keyword answered_by: The identifier of the call automation entity which answers the call.
        :paramtype answered_by:
         ~azure.communication.callautomation.models.CommunicationUserIdentifierModel
        :keyword media_streaming_options: Media Streaming Options.
        :paramtype media_streaming_options:
         ~azure.communication.callautomation.models.MediaStreamingOptions
        :keyword transcription_options: Transcription Options.
        :paramtype transcription_options:
         ~azure.communication.callautomation.models.TranscriptionOptions
        """
        super().__init__(**kwargs)
        self.incoming_call_context = incoming_call_context
        self.callback_uri = callback_uri
        self.operation_context = operation_context
        self.call_intelligence_options = call_intelligence_options
        self.answered_by = answered_by
        self.media_streaming_options = media_streaming_options
        self.transcription_options = transcription_options


class AnswerFailed(_serialization.Model):
    """The failed to answer call event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class CallConnected(_serialization.Model):
    """The call connected event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class CallConnectionProperties(_serialization.Model):
    """Properties of a call connection.

    :ivar call_connection_id: The call connection id.
    :vartype call_connection_id: str
    :ivar server_call_id: The server call id.
    :vartype server_call_id: str
    :ivar targets: The targets of the call.
    :vartype targets: list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
    :ivar call_connection_state: The state of the call connection. Known values are: "unknown",
     "connecting", "connected", "transferring", "transferAccepted", "disconnecting", and
     "disconnected".
    :vartype call_connection_state: str or
     ~azure.communication.callautomation.models.CallConnectionState
    :ivar callback_uri: The callback URI.
    :vartype callback_uri: str
    :ivar source_caller_id_number: The source caller Id, a phone number, that's shown to the PSTN
     participant being invited.
     Required only when calling a PSTN callee.
    :vartype source_caller_id_number:
     ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
    :ivar source_display_name: Display name of the call if dialing out to a pstn number.
    :vartype source_display_name: str
    :ivar source: Source identity.
    :vartype source: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar correlation_id: The correlation ID.
    :vartype correlation_id: str
    :ivar answered_by: Identity of the answering entity. Only populated when identity is provided
     in the request.
    :vartype answered_by:
     ~azure.communication.callautomation.models.CommunicationUserIdentifierModel
    :ivar media_streaming_subscription: The state of media streaming subscription for the call.
    :vartype media_streaming_subscription:
     ~azure.communication.callautomation.models.MediaStreamingSubscription
    :ivar transcription_subscription: Transcription Subscription.
    :vartype transcription_subscription:
     ~azure.communication.callautomation.models.TranscriptionSubscription
    :ivar answered_for: Identity of the original Pstn target of an incoming Call. Only populated
     when the original target is a Pstn number.
    :vartype answered_for: ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "targets": {"key": "targets", "type": "[CommunicationIdentifierModel]"},
        "call_connection_state": {"key": "callConnectionState", "type": "str"},
        "callback_uri": {"key": "callbackUri", "type": "str"},
        "source_caller_id_number": {"key": "sourceCallerIdNumber", "type": "PhoneNumberIdentifierModel"},
        "source_display_name": {"key": "sourceDisplayName", "type": "str"},
        "source": {"key": "source", "type": "CommunicationIdentifierModel"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "answered_by": {"key": "answeredBy", "type": "CommunicationUserIdentifierModel"},
        "media_streaming_subscription": {"key": "mediaStreamingSubscription", "type": "MediaStreamingSubscription"},
        "transcription_subscription": {"key": "transcriptionSubscription", "type": "TranscriptionSubscription"},
        "answered_for": {"key": "answeredFor", "type": "PhoneNumberIdentifierModel"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        targets: Optional[List["_models.CommunicationIdentifierModel"]] = None,
        call_connection_state: Optional[Union[str, "_models.CallConnectionState"]] = None,
        callback_uri: Optional[str] = None,
        source_caller_id_number: Optional["_models.PhoneNumberIdentifierModel"] = None,
        source_display_name: Optional[str] = None,
        source: Optional["_models.CommunicationIdentifierModel"] = None,
        correlation_id: Optional[str] = None,
        answered_by: Optional["_models.CommunicationUserIdentifierModel"] = None,
        media_streaming_subscription: Optional["_models.MediaStreamingSubscription"] = None,
        transcription_subscription: Optional["_models.TranscriptionSubscription"] = None,
        answered_for: Optional["_models.PhoneNumberIdentifierModel"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: The call connection id.
        :paramtype call_connection_id: str
        :keyword server_call_id: The server call id.
        :paramtype server_call_id: str
        :keyword targets: The targets of the call.
        :paramtype targets:
         list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
        :keyword call_connection_state: The state of the call connection. Known values are: "unknown",
         "connecting", "connected", "transferring", "transferAccepted", "disconnecting", and
         "disconnected".
        :paramtype call_connection_state: str or
         ~azure.communication.callautomation.models.CallConnectionState
        :keyword callback_uri: The callback URI.
        :paramtype callback_uri: str
        :keyword source_caller_id_number: The source caller Id, a phone number, that's shown to the
         PSTN participant being invited.
         Required only when calling a PSTN callee.
        :paramtype source_caller_id_number:
         ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
        :keyword source_display_name: Display name of the call if dialing out to a pstn number.
        :paramtype source_display_name: str
        :keyword source: Source identity.
        :paramtype source: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword correlation_id: The correlation ID.
        :paramtype correlation_id: str
        :keyword answered_by: Identity of the answering entity. Only populated when identity is
         provided in the request.
        :paramtype answered_by:
         ~azure.communication.callautomation.models.CommunicationUserIdentifierModel
        :keyword media_streaming_subscription: The state of media streaming subscription for the call.
        :paramtype media_streaming_subscription:
         ~azure.communication.callautomation.models.MediaStreamingSubscription
        :keyword transcription_subscription: Transcription Subscription.
        :paramtype transcription_subscription:
         ~azure.communication.callautomation.models.TranscriptionSubscription
        :keyword answered_for: Identity of the original Pstn target of an incoming Call. Only populated
         when the original target is a Pstn number.
        :paramtype answered_for: ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.targets = targets
        self.call_connection_state = call_connection_state
        self.callback_uri = callback_uri
        self.source_caller_id_number = source_caller_id_number
        self.source_display_name = source_display_name
        self.source = source
        self.correlation_id = correlation_id
        self.answered_by = answered_by
        self.media_streaming_subscription = media_streaming_subscription
        self.transcription_subscription = transcription_subscription
        self.answered_for = answered_for


class CallDisconnected(_serialization.Model):
    """The call disconnected event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class CallIntelligenceOptions(_serialization.Model):
    """AI options for the call.

    :ivar cognitive_services_endpoint: The identifier of the Cognitive Service resource assigned to
     this call.
    :vartype cognitive_services_endpoint: str
    """

    _attribute_map = {
        "cognitive_services_endpoint": {"key": "cognitiveServicesEndpoint", "type": "str"},
    }

    def __init__(self, *, cognitive_services_endpoint: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword cognitive_services_endpoint: The identifier of the Cognitive Service resource assigned
         to this call.
        :paramtype cognitive_services_endpoint: str
        """
        super().__init__(**kwargs)
        self.cognitive_services_endpoint = cognitive_services_endpoint


class CallLocator(_serialization.Model):
    """The locator used for joining or taking action on a call.

    :ivar group_call_id: The group call id.
    :vartype group_call_id: str
    :ivar server_call_id: The server call id.
    :vartype server_call_id: str
    :ivar room_id: The Acs room id. (Not supported for Start Recording).
    :vartype room_id: str
    :ivar kind: The call locator kind. Known values are: "groupCallLocator", "serverCallLocator",
     and "roomCallLocator".
    :vartype kind: str or ~azure.communication.callautomation.models.CallLocatorKind
    """

    _attribute_map = {
        "group_call_id": {"key": "groupCallId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "room_id": {"key": "roomId", "type": "str"},
        "kind": {"key": "kind", "type": "str"},
    }

    def __init__(
        self,
        *,
        group_call_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        room_id: Optional[str] = None,
        kind: Optional[Union[str, "_models.CallLocatorKind"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword group_call_id: The group call id.
        :paramtype group_call_id: str
        :keyword server_call_id: The server call id.
        :paramtype server_call_id: str
        :keyword room_id: The Acs room id. (Not supported for Start Recording).
        :paramtype room_id: str
        :keyword kind: The call locator kind. Known values are: "groupCallLocator",
         "serverCallLocator", and "roomCallLocator".
        :paramtype kind: str or ~azure.communication.callautomation.models.CallLocatorKind
        """
        super().__init__(**kwargs)
        self.group_call_id = group_call_id
        self.server_call_id = server_call_id
        self.room_id = room_id
        self.kind = kind


class CallParticipant(_serialization.Model):
    """A call participant.

    :ivar identifier: Communication identifier of the participant.
    :vartype identifier: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar is_muted: Is participant muted.
    :vartype is_muted: bool
    :ivar is_on_hold: Is participant on hold.
    :vartype is_on_hold: bool
    """

    _attribute_map = {
        "identifier": {"key": "identifier", "type": "CommunicationIdentifierModel"},
        "is_muted": {"key": "isMuted", "type": "bool"},
        "is_on_hold": {"key": "isOnHold", "type": "bool"},
    }

    def __init__(
        self,
        *,
        identifier: Optional["_models.CommunicationIdentifierModel"] = None,
        is_muted: Optional[bool] = None,
        is_on_hold: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword identifier: Communication identifier of the participant.
        :paramtype identifier: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword is_muted: Is participant muted.
        :paramtype is_muted: bool
        :keyword is_on_hold: Is participant on hold.
        :paramtype is_on_hold: bool
        """
        super().__init__(**kwargs)
        self.identifier = identifier
        self.is_muted = is_muted
        self.is_on_hold = is_on_hold


class CallTransferAccepted(_serialization.Model):
    """The call transfer accepted event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar transfer_target: Target who the call is transferred to.
    :vartype transfer_target:
     ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar transferee: the participant who is being transferred away.
    :vartype transferee: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "transfer_target": {"key": "transferTarget", "type": "CommunicationIdentifierModel"},
        "transferee": {"key": "transferee", "type": "CommunicationIdentifierModel"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        transfer_target: Optional["_models.CommunicationIdentifierModel"] = None,
        transferee: Optional["_models.CommunicationIdentifierModel"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword transfer_target: Target who the call is transferred to.
        :paramtype transfer_target:
         ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword transferee: the participant who is being transferred away.
        :paramtype transferee: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information
        self.transfer_target = transfer_target
        self.transferee = transferee


class CallTransferFailed(_serialization.Model):
    """The call transfer failed event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class CancelAddParticipantFailed(_serialization.Model):
    """Failed cancel add participant event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar invitation_id: Invitation ID used to cancel the request.
    :vartype invitation_id: str
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "invitation_id": {"key": "invitationId", "type": "str"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        invitation_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword invitation_id: Invitation ID used to cancel the request.
        :paramtype invitation_id: str
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information
        self.invitation_id = invitation_id


class CancelAddParticipantRequest(_serialization.Model):
    """Request payload for cancelling add participant request.

    All required parameters must be populated in order to send to server.

    :ivar invitation_id: Invitation ID used to add a participant. Required.
    :vartype invitation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _validation = {
        "invitation_id": {"required": True},
    }

    _attribute_map = {
        "invitation_id": {"key": "invitationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        invitation_id: str,
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword invitation_id: Invitation ID used to add a participant. Required.
        :paramtype invitation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.invitation_id = invitation_id
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class CancelAddParticipantResponse(_serialization.Model):
    """Response payload for cancel add participant request.

    :ivar invitation_id: Invitation ID used to cancel the add participant action.
    :vartype invitation_id: str
    :ivar operation_context: The operation context provided by client.
    :vartype operation_context: str
    """

    _attribute_map = {
        "invitation_id": {"key": "invitationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
    }

    def __init__(
        self, *, invitation_id: Optional[str] = None, operation_context: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword invitation_id: Invitation ID used to cancel the add participant action.
        :paramtype invitation_id: str
        :keyword operation_context: The operation context provided by client.
        :paramtype operation_context: str
        """
        super().__init__(**kwargs)
        self.invitation_id = invitation_id
        self.operation_context = operation_context


class CancelAddParticipantSucceeded(_serialization.Model):
    """Successful cancel add participant event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar invitation_id: Invitation ID used to cancel the request.
    :vartype invitation_id: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "invitation_id": {"key": "invitationId", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        invitation_id: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword invitation_id: Invitation ID used to cancel the request.
        :paramtype invitation_id: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.invitation_id = invitation_id
        self.result_information = result_information


class ChannelAffinity(_serialization.Model):
    """Channel affinity for a participant.

    All required parameters must be populated in order to send to server.

    :ivar channel: Channel number to which bitstream from a particular participant will be written.
    :vartype channel: int
    :ivar participant: The identifier for the participant whose bitstream will be written to the
     channel
     represented by the channel number. Required.
    :vartype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    """

    _validation = {
        "channel": {"maximum": 4, "minimum": 0},
        "participant": {"required": True},
    }

    _attribute_map = {
        "channel": {"key": "channel", "type": "int"},
        "participant": {"key": "participant", "type": "CommunicationIdentifierModel"},
    }

    def __init__(
        self, *, participant: "_models.CommunicationIdentifierModel", channel: Optional[int] = None, **kwargs: Any
    ) -> None:
        """
        :keyword channel: Channel number to which bitstream from a particular participant will be
         written.
        :paramtype channel: int
        :keyword participant: The identifier for the participant whose bitstream will be written to the
         channel
         represented by the channel number. Required.
        :paramtype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        """
        super().__init__(**kwargs)
        self.channel = channel
        self.participant = participant


class Choice(_serialization.Model):
    """Choice.

    All required parameters must be populated in order to send to server.

    :ivar label: Identifier for a given choice. Required.
    :vartype label: str
    :ivar phrases: List of phrases to recognize. Required.
    :vartype phrases: list[str]
    :ivar tone: Known values are: "zero", "one", "two", "three", "four", "five", "six", "seven",
     "eight", "nine", "a", "b", "c", "d", "pound", and "asterisk".
    :vartype tone: str or ~azure.communication.callautomation.models.DtmfTone
    """

    _validation = {
        "label": {"required": True},
        "phrases": {"required": True},
    }

    _attribute_map = {
        "label": {"key": "label", "type": "str"},
        "phrases": {"key": "phrases", "type": "[str]"},
        "tone": {"key": "tone", "type": "str"},
    }

    def __init__(
        self, *, label: str, phrases: List[str], tone: Optional[Union[str, "_models.DtmfTone"]] = None, **kwargs: Any
    ) -> None:
        """
        :keyword label: Identifier for a given choice. Required.
        :paramtype label: str
        :keyword phrases: List of phrases to recognize. Required.
        :paramtype phrases: list[str]
        :keyword tone: Known values are: "zero", "one", "two", "three", "four", "five", "six", "seven",
         "eight", "nine", "a", "b", "c", "d", "pound", and "asterisk".
        :paramtype tone: str or ~azure.communication.callautomation.models.DtmfTone
        """
        super().__init__(**kwargs)
        self.label = label
        self.phrases = phrases
        self.tone = tone


class ChoiceResult(_serialization.Model):
    """ChoiceResult.

    :ivar label: Label is the primary identifier for the choice detected.
    :vartype label: str
    :ivar recognized_phrase: Phrases are set to the value if choice is selected via phrase
     detection.
     If Dtmf input is recognized, then Label will be the identifier for the choice detected and
     phrases will be set to null.
    :vartype recognized_phrase: str
    :ivar confidence: The confidence level of the recognized speech, if available, ranges from 0.0
     to 1.0.
    :vartype confidence: float
    """

    _attribute_map = {
        "label": {"key": "label", "type": "str"},
        "recognized_phrase": {"key": "recognizedPhrase", "type": "str"},
        "confidence": {"key": "confidence", "type": "float"},
    }

    def __init__(
        self,
        *,
        label: Optional[str] = None,
        recognized_phrase: Optional[str] = None,
        confidence: Optional[float] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword label: Label is the primary identifier for the choice detected.
        :paramtype label: str
        :keyword recognized_phrase: Phrases are set to the value if choice is selected via phrase
         detection.
         If Dtmf input is recognized, then Label will be the identifier for the choice detected and
         phrases will be set to null.
        :paramtype recognized_phrase: str
        :keyword confidence: The confidence level of the recognized speech, if available, ranges from
         0.0 to 1.0.
        :paramtype confidence: float
        """
        super().__init__(**kwargs)
        self.label = label
        self.recognized_phrase = recognized_phrase
        self.confidence = confidence


class CommunicationError(_serialization.Model):
    """The Communication Services error.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar code: The error code. Required.
    :vartype code: str
    :ivar message: The error message. Required.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: Further details about specific errors that led to this error.
    :vartype details: list[~azure.communication.callautomation.models.CommunicationError]
    :ivar inner_error: The inner error if any.
    :vartype inner_error: ~azure.communication.callautomation.models.CommunicationError
    """

    _validation = {
        "code": {"required": True},
        "message": {"required": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "inner_error": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[CommunicationError]"},
        "inner_error": {"key": "innererror", "type": "CommunicationError"},
    }

    def __init__(self, *, code: str, message: str, **kwargs: Any) -> None:
        """
        :keyword code: The error code. Required.
        :paramtype code: str
        :keyword message: The error message. Required.
        :paramtype message: str
        """
        super().__init__(**kwargs)
        self.code = code
        self.message = message
        self.target: Optional[str] = None
        self.details: Optional[List["_models.CommunicationError"]] = None
        self.inner_error: Optional["_models.CommunicationError"] = None


class CommunicationErrorResponse(_serialization.Model):
    """The Communication Services error.

    All required parameters must be populated in order to send to server.

    :ivar error: The Communication Services error. Required.
    :vartype error: ~azure.communication.callautomation.models.CommunicationError
    """

    _validation = {
        "error": {"required": True},
    }

    _attribute_map = {
        "error": {"key": "error", "type": "CommunicationError"},
    }

    def __init__(self, *, error: "_models.CommunicationError", **kwargs: Any) -> None:
        """
        :keyword error: The Communication Services error. Required.
        :paramtype error: ~azure.communication.callautomation.models.CommunicationError
        """
        super().__init__(**kwargs)
        self.error = error


class CommunicationIdentifierModel(_serialization.Model):
    """Identifies a participant in Azure Communication services. A participant is, for example, a
    phone number or an Azure communication user. This model is polymorphic: Apart from kind and
    rawId, at most one further property may be set which must match the kind enum value.

    :ivar kind: The identifier kind. Only required in responses. Known values are: "unknown",
     "communicationUser", "phoneNumber", "microsoftTeamsUser", and "microsoftTeamsApp".
    :vartype kind: str or
     ~azure.communication.callautomation.models.CommunicationIdentifierModelKind
    :ivar raw_id: Raw Id of the identifier. Optional in requests, required in responses.
    :vartype raw_id: str
    :ivar communication_user: The communication user.
    :vartype communication_user:
     ~azure.communication.callautomation.models.CommunicationUserIdentifierModel
    :ivar phone_number: The phone number.
    :vartype phone_number: ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
    :ivar microsoft_teams_user: The Microsoft Teams user.
    :vartype microsoft_teams_user:
     ~azure.communication.callautomation.models.MicrosoftTeamsUserIdentifierModel
    :ivar microsoft_teams_app: The Microsoft Teams application.
    :vartype microsoft_teams_app:
     ~azure.communication.callautomation.models.MicrosoftTeamsAppIdentifierModel
    """

    _attribute_map = {
        "kind": {"key": "kind", "type": "str"},
        "raw_id": {"key": "rawId", "type": "str"},
        "communication_user": {"key": "communicationUser", "type": "CommunicationUserIdentifierModel"},
        "phone_number": {"key": "phoneNumber", "type": "PhoneNumberIdentifierModel"},
        "microsoft_teams_user": {"key": "microsoftTeamsUser", "type": "MicrosoftTeamsUserIdentifierModel"},
        "microsoft_teams_app": {"key": "microsoftTeamsApp", "type": "MicrosoftTeamsAppIdentifierModel"},
    }

    def __init__(
        self,
        *,
        kind: Optional[Union[str, "_models.CommunicationIdentifierModelKind"]] = None,
        raw_id: Optional[str] = None,
        communication_user: Optional["_models.CommunicationUserIdentifierModel"] = None,
        phone_number: Optional["_models.PhoneNumberIdentifierModel"] = None,
        microsoft_teams_user: Optional["_models.MicrosoftTeamsUserIdentifierModel"] = None,
        microsoft_teams_app: Optional["_models.MicrosoftTeamsAppIdentifierModel"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword kind: The identifier kind. Only required in responses. Known values are: "unknown",
         "communicationUser", "phoneNumber", "microsoftTeamsUser", and "microsoftTeamsApp".
        :paramtype kind: str or
         ~azure.communication.callautomation.models.CommunicationIdentifierModelKind
        :keyword raw_id: Raw Id of the identifier. Optional in requests, required in responses.
        :paramtype raw_id: str
        :keyword communication_user: The communication user.
        :paramtype communication_user:
         ~azure.communication.callautomation.models.CommunicationUserIdentifierModel
        :keyword phone_number: The phone number.
        :paramtype phone_number: ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
        :keyword microsoft_teams_user: The Microsoft Teams user.
        :paramtype microsoft_teams_user:
         ~azure.communication.callautomation.models.MicrosoftTeamsUserIdentifierModel
        :keyword microsoft_teams_app: The Microsoft Teams application.
        :paramtype microsoft_teams_app:
         ~azure.communication.callautomation.models.MicrosoftTeamsAppIdentifierModel
        """
        super().__init__(**kwargs)
        self.kind = kind
        self.raw_id = raw_id
        self.communication_user = communication_user
        self.phone_number = phone_number
        self.microsoft_teams_user = microsoft_teams_user
        self.microsoft_teams_app = microsoft_teams_app


class CommunicationUserIdentifierModel(_serialization.Model):
    """A user that got created with an Azure Communication Services resource.

    All required parameters must be populated in order to send to server.

    :ivar id: The Id of the communication user. Required.
    :vartype id: str
    """

    _validation = {
        "id": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
    }

    def __init__(self, *, id: str, **kwargs: Any) -> None:  # pylint: disable=redefined-builtin
        """
        :keyword id: The Id of the communication user. Required.
        :paramtype id: str
        """
        super().__init__(**kwargs)
        self.id = id


class ConnectFailed(_serialization.Model):
    """The ConnectFailed event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class ConnectRequest(_serialization.Model):
    """The request payload for creating a connection to a CallLocator.

    All required parameters must be populated in order to send to server.

    :ivar call_locator: The call locator. Required.
    :vartype call_locator: ~azure.communication.callautomation.models.CallLocator
    :ivar callback_uri: The callback URI. Required.
    :vartype callback_uri: str
    :ivar operation_context: Used by customers to correlate the request to the response event.
    :vartype operation_context: str
    :ivar call_intelligence_options: AI options for the call.
    :vartype call_intelligence_options:
     ~azure.communication.callautomation.models.CallIntelligenceOptions
    :ivar media_streaming_options: Media Streaming Options.
    :vartype media_streaming_options:
     ~azure.communication.callautomation.models.MediaStreamingOptions
    :ivar transcription_options: Transcription Options.
    :vartype transcription_options: ~azure.communication.callautomation.models.TranscriptionOptions
    """

    _validation = {
        "call_locator": {"required": True},
        "callback_uri": {"required": True},
    }

    _attribute_map = {
        "call_locator": {"key": "callLocator", "type": "CallLocator"},
        "callback_uri": {"key": "callbackUri", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "call_intelligence_options": {"key": "callIntelligenceOptions", "type": "CallIntelligenceOptions"},
        "media_streaming_options": {"key": "mediaStreamingOptions", "type": "MediaStreamingOptions"},
        "transcription_options": {"key": "transcriptionOptions", "type": "TranscriptionOptions"},
    }

    def __init__(
        self,
        *,
        call_locator: "_models.CallLocator",
        callback_uri: str,
        operation_context: Optional[str] = None,
        call_intelligence_options: Optional["_models.CallIntelligenceOptions"] = None,
        media_streaming_options: Optional["_models.MediaStreamingOptions"] = None,
        transcription_options: Optional["_models.TranscriptionOptions"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_locator: The call locator. Required.
        :paramtype call_locator: ~azure.communication.callautomation.models.CallLocator
        :keyword callback_uri: The callback URI. Required.
        :paramtype callback_uri: str
        :keyword operation_context: Used by customers to correlate the request to the response event.
        :paramtype operation_context: str
        :keyword call_intelligence_options: AI options for the call.
        :paramtype call_intelligence_options:
         ~azure.communication.callautomation.models.CallIntelligenceOptions
        :keyword media_streaming_options: Media Streaming Options.
        :paramtype media_streaming_options:
         ~azure.communication.callautomation.models.MediaStreamingOptions
        :keyword transcription_options: Transcription Options.
        :paramtype transcription_options:
         ~azure.communication.callautomation.models.TranscriptionOptions
        """
        super().__init__(**kwargs)
        self.call_locator = call_locator
        self.callback_uri = callback_uri
        self.operation_context = operation_context
        self.call_intelligence_options = call_intelligence_options
        self.media_streaming_options = media_streaming_options
        self.transcription_options = transcription_options


class ContinuousDtmfRecognitionRequest(_serialization.Model):
    """ContinuousDtmfRecognitionRequest.

    All required parameters must be populated in order to send to server.

    :ivar target_participant: Defines options for recognition. Required.
    :vartype target_participant:
     ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar operation_context: The value to identify context of the operation.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _validation = {
        "target_participant": {"required": True},
    }

    _attribute_map = {
        "target_participant": {"key": "targetParticipant", "type": "CommunicationIdentifierModel"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        target_participant: "_models.CommunicationIdentifierModel",
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword target_participant: Defines options for recognition. Required.
        :paramtype target_participant:
         ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword operation_context: The value to identify context of the operation.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.target_participant = target_participant
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class ContinuousDtmfRecognitionStopped(_serialization.Model):
    """ContinuousDtmfRecognitionStopped.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class ContinuousDtmfRecognitionToneFailed(_serialization.Model):
    """ContinuousDtmfRecognitionToneFailed.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "operation_context": {"key": "operationContext", "type": "str"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        operation_context: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.result_information = result_information
        self.operation_context = operation_context


class ContinuousDtmfRecognitionToneReceived(_serialization.Model):
    """ContinuousDtmfRecognitionToneReceived.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar sequence_id: The sequence id which can be used to determine if the same tone was played
     multiple times or if any tones were missed.
    :vartype sequence_id: int
    :ivar tone: Known values are: "zero", "one", "two", "three", "four", "five", "six", "seven",
     "eight", "nine", "a", "b", "c", "d", "pound", and "asterisk".
    :vartype tone: str or ~azure.communication.callautomation.models.DtmfTone
    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _validation = {
        "sequence_id": {"readonly": True},
    }

    _attribute_map = {
        "sequence_id": {"key": "sequenceId", "type": "int"},
        "tone": {"key": "tone", "type": "str"},
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        tone: Optional[Union[str, "_models.DtmfTone"]] = None,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tone: Known values are: "zero", "one", "two", "three", "four", "five", "six", "seven",
         "eight", "nine", "a", "b", "c", "d", "pound", and "asterisk".
        :paramtype tone: str or ~azure.communication.callautomation.models.DtmfTone
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.sequence_id: Optional[int] = None
        self.tone = tone
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class CreateCallFailed(_serialization.Model):
    """The create call failed event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class CreateCallRequest(_serialization.Model):
    """The request payload for creating the call.

    All required parameters must be populated in order to send to server.

    :ivar targets: The targets of the call. Required.
    :vartype targets: list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
    :ivar source_caller_id_number: The source caller Id, a phone number, that's shown to the PSTN
     participant being invited.
     Required only when calling a PSTN callee.
    :vartype source_caller_id_number:
     ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
    :ivar source_display_name: Display name of the call if dialing out to a pstn number.
    :vartype source_display_name: str
    :ivar source: The identifier of the source of the call.
    :vartype source: ~azure.communication.callautomation.models.CommunicationUserIdentifierModel
    :ivar operation_context: A customer set value used to track the answering of a call.
    :vartype operation_context: str
    :ivar callback_uri: The callback URI. Required.
    :vartype callback_uri: str
    :ivar call_intelligence_options: AI options for the call.
    :vartype call_intelligence_options:
     ~azure.communication.callautomation.models.CallIntelligenceOptions
    :ivar media_streaming_options: Media Streaming Options.
    :vartype media_streaming_options:
     ~azure.communication.callautomation.models.MediaStreamingOptions
    :ivar transcription_options: Transcription Options.
    :vartype transcription_options: ~azure.communication.callautomation.models.TranscriptionOptions
    """

    _validation = {
        "targets": {"required": True},
        "callback_uri": {"required": True},
    }

    _attribute_map = {
        "targets": {"key": "targets", "type": "[CommunicationIdentifierModel]"},
        "source_caller_id_number": {"key": "sourceCallerIdNumber", "type": "PhoneNumberIdentifierModel"},
        "source_display_name": {"key": "sourceDisplayName", "type": "str"},
        "source": {"key": "source", "type": "CommunicationUserIdentifierModel"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "callback_uri": {"key": "callbackUri", "type": "str"},
        "call_intelligence_options": {"key": "callIntelligenceOptions", "type": "CallIntelligenceOptions"},
        "media_streaming_options": {"key": "mediaStreamingOptions", "type": "MediaStreamingOptions"},
        "transcription_options": {"key": "transcriptionOptions", "type": "TranscriptionOptions"},
    }

    def __init__(
        self,
        *,
        targets: List["_models.CommunicationIdentifierModel"],
        callback_uri: str,
        source_caller_id_number: Optional["_models.PhoneNumberIdentifierModel"] = None,
        source_display_name: Optional[str] = None,
        source: Optional["_models.CommunicationUserIdentifierModel"] = None,
        operation_context: Optional[str] = None,
        call_intelligence_options: Optional["_models.CallIntelligenceOptions"] = None,
        media_streaming_options: Optional["_models.MediaStreamingOptions"] = None,
        transcription_options: Optional["_models.TranscriptionOptions"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword targets: The targets of the call. Required.
        :paramtype targets:
         list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
        :keyword source_caller_id_number: The source caller Id, a phone number, that's shown to the
         PSTN participant being invited.
         Required only when calling a PSTN callee.
        :paramtype source_caller_id_number:
         ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
        :keyword source_display_name: Display name of the call if dialing out to a pstn number.
        :paramtype source_display_name: str
        :keyword source: The identifier of the source of the call.
        :paramtype source: ~azure.communication.callautomation.models.CommunicationUserIdentifierModel
        :keyword operation_context: A customer set value used to track the answering of a call.
        :paramtype operation_context: str
        :keyword callback_uri: The callback URI. Required.
        :paramtype callback_uri: str
        :keyword call_intelligence_options: AI options for the call.
        :paramtype call_intelligence_options:
         ~azure.communication.callautomation.models.CallIntelligenceOptions
        :keyword media_streaming_options: Media Streaming Options.
        :paramtype media_streaming_options:
         ~azure.communication.callautomation.models.MediaStreamingOptions
        :keyword transcription_options: Transcription Options.
        :paramtype transcription_options:
         ~azure.communication.callautomation.models.TranscriptionOptions
        """
        super().__init__(**kwargs)
        self.targets = targets
        self.source_caller_id_number = source_caller_id_number
        self.source_display_name = source_display_name
        self.source = source
        self.operation_context = operation_context
        self.callback_uri = callback_uri
        self.call_intelligence_options = call_intelligence_options
        self.media_streaming_options = media_streaming_options
        self.transcription_options = transcription_options


class CustomCallingContext(_serialization.Model):
    """The custom calling context which will be sent to the target.

    :ivar voip_headers: Custom calling context VoiP headers.
    :vartype voip_headers: dict[str, str]
    :ivar sip_headers: Custom calling context SIP headers.
    :vartype sip_headers: dict[str, str]
    """

    _attribute_map = {
        "voip_headers": {"key": "voipHeaders", "type": "{str}"},
        "sip_headers": {"key": "sipHeaders", "type": "{str}"},
    }

    def __init__(
        self,
        *,
        voip_headers: Optional[Dict[str, str]] = None,
        sip_headers: Optional[Dict[str, str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword voip_headers: Custom calling context VoiP headers.
        :paramtype voip_headers: dict[str, str]
        :keyword sip_headers: Custom calling context SIP headers.
        :paramtype sip_headers: dict[str, str]
        """
        super().__init__(**kwargs)
        self.voip_headers = voip_headers
        self.sip_headers = sip_headers


class DtmfOptions(_serialization.Model):
    """Options for DTMF recognition.

    :ivar inter_tone_timeout_in_seconds: Time to wait between DTMF inputs to stop recognizing.
    :vartype inter_tone_timeout_in_seconds: int
    :ivar max_tones_to_collect: Maximum number of DTMF tones to be collected.
    :vartype max_tones_to_collect: int
    :ivar stop_tones: List of tones that will stop recognizing.
    :vartype stop_tones: list[str or ~azure.communication.callautomation.models.DtmfTone]
    """

    _validation = {
        "inter_tone_timeout_in_seconds": {"maximum": 60, "minimum": 1},
    }

    _attribute_map = {
        "inter_tone_timeout_in_seconds": {"key": "interToneTimeoutInSeconds", "type": "int"},
        "max_tones_to_collect": {"key": "maxTonesToCollect", "type": "int"},
        "stop_tones": {"key": "stopTones", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        inter_tone_timeout_in_seconds: Optional[int] = None,
        max_tones_to_collect: Optional[int] = None,
        stop_tones: Optional[List[Union[str, "_models.DtmfTone"]]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword inter_tone_timeout_in_seconds: Time to wait between DTMF inputs to stop recognizing.
        :paramtype inter_tone_timeout_in_seconds: int
        :keyword max_tones_to_collect: Maximum number of DTMF tones to be collected.
        :paramtype max_tones_to_collect: int
        :keyword stop_tones: List of tones that will stop recognizing.
        :paramtype stop_tones: list[str or ~azure.communication.callautomation.models.DtmfTone]
        """
        super().__init__(**kwargs)
        self.inter_tone_timeout_in_seconds = inter_tone_timeout_in_seconds
        self.max_tones_to_collect = max_tones_to_collect
        self.stop_tones = stop_tones


class DtmfResult(_serialization.Model):
    """DtmfResult.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar tones:
    :vartype tones: list[str or ~azure.communication.callautomation.models.DtmfTone]
    """

    _validation = {
        "tones": {"readonly": True},
    }

    _attribute_map = {
        "tones": {"key": "tones", "type": "[str]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.tones: Optional[List[Union[str, "_models.DtmfTone"]]] = None


class ExternalStorage(_serialization.Model):
    """ExternalStorage.

    All required parameters must be populated in order to send to server.

    :ivar recording_storage_kind: Defines the kind of external storage. Required. Known values are:
     "AzureCommunicationServices" and "AzureBlobStorage".
    :vartype recording_storage_kind: str or
     ~azure.communication.callautomation.models.RecordingStorageKind
    :ivar recording_destination_container_url: Uri of a container or a location within a container.
    :vartype recording_destination_container_url: str
    """

    _validation = {
        "recording_storage_kind": {"required": True},
    }

    _attribute_map = {
        "recording_storage_kind": {"key": "recordingStorageKind", "type": "str"},
        "recording_destination_container_url": {"key": "recordingDestinationContainerUrl", "type": "str"},
    }

    def __init__(
        self,
        *,
        recording_storage_kind: Union[str, "_models.RecordingStorageKind"],
        recording_destination_container_url: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword recording_storage_kind: Defines the kind of external storage. Required. Known values
         are: "AzureCommunicationServices" and "AzureBlobStorage".
        :paramtype recording_storage_kind: str or
         ~azure.communication.callautomation.models.RecordingStorageKind
        :keyword recording_destination_container_url: Uri of a container or a location within a
         container.
        :paramtype recording_destination_container_url: str
        """
        super().__init__(**kwargs)
        self.recording_storage_kind = recording_storage_kind
        self.recording_destination_container_url = recording_destination_container_url


class FileSource(_serialization.Model):
    """FileSource.

    All required parameters must be populated in order to send to server.

    :ivar uri: Uri for the audio file to be played. Required.
    :vartype uri: str
    """

    _validation = {
        "uri": {"required": True},
    }

    _attribute_map = {
        "uri": {"key": "uri", "type": "str"},
    }

    def __init__(self, *, uri: str, **kwargs: Any) -> None:
        """
        :keyword uri: Uri for the audio file to be played. Required.
        :paramtype uri: str
        """
        super().__init__(**kwargs)
        self.uri = uri


class GetParticipantsResponse(_serialization.Model):
    """The response payload for getting participants of the call.

    All required parameters must be populated in order to send to server.

    :ivar value: List of the current participants in the call. Required.
    :vartype value: list[~azure.communication.callautomation.models.CallParticipant]
    :ivar next_link: Continue of the list of participants.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[CallParticipant]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: List["_models.CallParticipant"], next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: List of the current participants in the call. Required.
        :paramtype value: list[~azure.communication.callautomation.models.CallParticipant]
        :keyword next_link: Continue of the list of participants.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class HoldFailed(_serialization.Model):
    """HoldFailed.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class HoldRequest(_serialization.Model):
    """The request payload for holding participant from the call.

    All required parameters must be populated in order to send to server.

    :ivar target_participant: Participant to be held from the call. Required.
    :vartype target_participant:
     ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar play_source_info: Prompt to play while in hold.
    :vartype play_source_info: ~azure.communication.callautomation.models.PlaySource
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _validation = {
        "target_participant": {"required": True},
    }

    _attribute_map = {
        "target_participant": {"key": "targetParticipant", "type": "CommunicationIdentifierModel"},
        "play_source_info": {"key": "playSourceInfo", "type": "PlaySource"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        target_participant: "_models.CommunicationIdentifierModel",
        play_source_info: Optional["_models.PlaySource"] = None,
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword target_participant: Participant to be held from the call. Required.
        :paramtype target_participant:
         ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword play_source_info: Prompt to play while in hold.
        :paramtype play_source_info: ~azure.communication.callautomation.models.PlaySource
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.target_participant = target_participant
        self.play_source_info = play_source_info
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class MediaStreamingFailed(_serialization.Model):
    """MediaStreamingFailed.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar media_streaming_update: Defines the result for MediaStreamingUpdate with the current
     status and the details about the status.
    :vartype media_streaming_update:
     ~azure.communication.callautomation.models.MediaStreamingUpdate
    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _validation = {
        "media_streaming_update": {"readonly": True},
    }

    _attribute_map = {
        "media_streaming_update": {"key": "mediaStreamingUpdate", "type": "MediaStreamingUpdate"},
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.media_streaming_update: Optional["_models.MediaStreamingUpdate"] = None
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class MediaStreamingOptions(_serialization.Model):
    """Options for media streaming.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    WebSocketMediaStreamingOptions

    All required parameters must be populated in order to send to server.

    :ivar transport_type: Defines the transport type used for streaming. Note that future values
     may be introduced that are not currently documented. Required. "websocket"
    :vartype transport_type: str or
     ~azure.communication.callautomation.models.StreamingTransportType
    :ivar audio_channel_type: The audio channel type to stream, e.g., unmixed audio, mixed audio.
     Required. Known values are: "mixed" and "unmixed".
    :vartype audio_channel_type: str or
     ~azure.communication.callautomation.models.MediaStreamingAudioChannelType
    """

    _validation = {
        "transport_type": {"required": True},
        "audio_channel_type": {"required": True},
    }

    _attribute_map = {
        "transport_type": {"key": "transportType", "type": "str"},
        "audio_channel_type": {"key": "audioChannelType", "type": "str"},
    }

    _subtype_map = {"transport_type": {"websocket": "WebSocketMediaStreamingOptions"}}

    def __init__(
        self, *, audio_channel_type: Union[str, "_models.MediaStreamingAudioChannelType"], **kwargs: Any
    ) -> None:
        """
        :keyword audio_channel_type: The audio channel type to stream, e.g., unmixed audio, mixed
         audio. Required. Known values are: "mixed" and "unmixed".
        :paramtype audio_channel_type: str or
         ~azure.communication.callautomation.models.MediaStreamingAudioChannelType
        """
        super().__init__(**kwargs)
        self.transport_type: Optional[str] = None
        self.audio_channel_type = audio_channel_type


class MediaStreamingStarted(_serialization.Model):
    """MediaStreamingStarted.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar media_streaming_update: Defines the result for MediaStreamingUpdate with the current
     status and the details about the status.
    :vartype media_streaming_update:
     ~azure.communication.callautomation.models.MediaStreamingUpdate
    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _validation = {
        "media_streaming_update": {"readonly": True},
    }

    _attribute_map = {
        "media_streaming_update": {"key": "mediaStreamingUpdate", "type": "MediaStreamingUpdate"},
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.media_streaming_update: Optional["_models.MediaStreamingUpdate"] = None
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class MediaStreamingStopped(_serialization.Model):
    """MediaStreamingStopped.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar media_streaming_update: Defines the result for MediaStreamingUpdate with the current
     status and the details about the status.
    :vartype media_streaming_update:
     ~azure.communication.callautomation.models.MediaStreamingUpdate
    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _validation = {
        "media_streaming_update": {"readonly": True},
    }

    _attribute_map = {
        "media_streaming_update": {"key": "mediaStreamingUpdate", "type": "MediaStreamingUpdate"},
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.media_streaming_update: Optional["_models.MediaStreamingUpdate"] = None
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class MediaStreamingSubscription(_serialization.Model):
    """Media streaming Subscription Object.

    :ivar id: Subscription Id.
    :vartype id: str
    :ivar state: Media streaming subscription state. Known values are: "disabled", "inactive", and
     "active".
    :vartype state: str or
     ~azure.communication.callautomation.models.MediaStreamingSubscriptionState
    :ivar subscribed_content_types: Subscribed media streaming content types.
    :vartype subscribed_content_types: list[str or
     ~azure.communication.callautomation.models.MediaStreamingContentType]
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "state": {"key": "state", "type": "str"},
        "subscribed_content_types": {"key": "subscribedContentTypes", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        state: Optional[Union[str, "_models.MediaStreamingSubscriptionState"]] = None,
        subscribed_content_types: Optional[List[Union[str, "_models.MediaStreamingContentType"]]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Subscription Id.
        :paramtype id: str
        :keyword state: Media streaming subscription state. Known values are: "disabled", "inactive",
         and "active".
        :paramtype state: str or
         ~azure.communication.callautomation.models.MediaStreamingSubscriptionState
        :keyword subscribed_content_types: Subscribed media streaming content types.
        :paramtype subscribed_content_types: list[str or
         ~azure.communication.callautomation.models.MediaStreamingContentType]
        """
        super().__init__(**kwargs)
        self.id = id
        self.state = state
        self.subscribed_content_types = subscribed_content_types


class MediaStreamingUpdate(_serialization.Model):
    """MediaStreamingUpdate.

    :ivar content_type:
    :vartype content_type: str
    :ivar media_streaming_status: Known values are: "mediaStreamingStarted",
     "mediaStreamingFailed", "mediaStreamingStopped", and "unspecifiedError".
    :vartype media_streaming_status: str or
     ~azure.communication.callautomation.models.MediaStreamingStatus
    :ivar media_streaming_status_details: Known values are: "subscriptionStarted",
     "streamConnectionReestablished", "streamConnectionUnsuccessful", "streamUrlMissing",
     "serviceShutdown", "streamConnectionInterrupted", "speechServicesConnectionError",
     "subscriptionStopped", "unspecifiedError", "authenticationFailure", "badRequest",
     "tooManyRequests", "forbidden", "serviceTimeout", and "initialWebSocketConnectionFailed".
    :vartype media_streaming_status_details: str or
     ~azure.communication.callautomation.models.MediaStreamingStatusDetails
    """

    _attribute_map = {
        "content_type": {"key": "contentType", "type": "str"},
        "media_streaming_status": {"key": "mediaStreamingStatus", "type": "str"},
        "media_streaming_status_details": {"key": "mediaStreamingStatusDetails", "type": "str"},
    }

    def __init__(
        self,
        *,
        content_type: Optional[str] = None,
        media_streaming_status: Optional[Union[str, "_models.MediaStreamingStatus"]] = None,
        media_streaming_status_details: Optional[Union[str, "_models.MediaStreamingStatusDetails"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword content_type:
        :paramtype content_type: str
        :keyword media_streaming_status: Known values are: "mediaStreamingStarted",
         "mediaStreamingFailed", "mediaStreamingStopped", and "unspecifiedError".
        :paramtype media_streaming_status: str or
         ~azure.communication.callautomation.models.MediaStreamingStatus
        :keyword media_streaming_status_details: Known values are: "subscriptionStarted",
         "streamConnectionReestablished", "streamConnectionUnsuccessful", "streamUrlMissing",
         "serviceShutdown", "streamConnectionInterrupted", "speechServicesConnectionError",
         "subscriptionStopped", "unspecifiedError", "authenticationFailure", "badRequest",
         "tooManyRequests", "forbidden", "serviceTimeout", and "initialWebSocketConnectionFailed".
        :paramtype media_streaming_status_details: str or
         ~azure.communication.callautomation.models.MediaStreamingStatusDetails
        """
        super().__init__(**kwargs)
        self.content_type = content_type
        self.media_streaming_status = media_streaming_status
        self.media_streaming_status_details = media_streaming_status_details


class MicrosoftTeamsAppIdentifierModel(_serialization.Model):
    """A Microsoft Teams application.

    All required parameters must be populated in order to send to server.

    :ivar app_id: The Id of the Microsoft Teams application. Required.
    :vartype app_id: str
    :ivar cloud: The cloud that the Microsoft Teams application belongs to. By default 'public' if
     missing. Known values are: "public", "dod", and "gcch".
    :vartype cloud: str or
     ~azure.communication.callautomation.models.CommunicationCloudEnvironmentModel
    """

    _validation = {
        "app_id": {"required": True},
    }

    _attribute_map = {
        "app_id": {"key": "appId", "type": "str"},
        "cloud": {"key": "cloud", "type": "str"},
    }

    def __init__(
        self,
        *,
        app_id: str,
        cloud: Optional[Union[str, "_models.CommunicationCloudEnvironmentModel"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword app_id: The Id of the Microsoft Teams application. Required.
        :paramtype app_id: str
        :keyword cloud: The cloud that the Microsoft Teams application belongs to. By default 'public'
         if missing. Known values are: "public", "dod", and "gcch".
        :paramtype cloud: str or
         ~azure.communication.callautomation.models.CommunicationCloudEnvironmentModel
        """
        super().__init__(**kwargs)
        self.app_id = app_id
        self.cloud = cloud


class MicrosoftTeamsUserIdentifierModel(_serialization.Model):
    """A Microsoft Teams user.

    All required parameters must be populated in order to send to server.

    :ivar user_id: The Id of the Microsoft Teams user. If not anonymous, this is the AAD object Id
     of the user. Required.
    :vartype user_id: str
    :ivar is_anonymous: True if the Microsoft Teams user is anonymous. By default false if missing.
    :vartype is_anonymous: bool
    :ivar cloud: The cloud that the Microsoft Teams user belongs to. By default 'public' if
     missing. Known values are: "public", "dod", and "gcch".
    :vartype cloud: str or
     ~azure.communication.callautomation.models.CommunicationCloudEnvironmentModel
    """

    _validation = {
        "user_id": {"required": True},
    }

    _attribute_map = {
        "user_id": {"key": "userId", "type": "str"},
        "is_anonymous": {"key": "isAnonymous", "type": "bool"},
        "cloud": {"key": "cloud", "type": "str"},
    }

    def __init__(
        self,
        *,
        user_id: str,
        is_anonymous: Optional[bool] = None,
        cloud: Optional[Union[str, "_models.CommunicationCloudEnvironmentModel"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword user_id: The Id of the Microsoft Teams user. If not anonymous, this is the AAD object
         Id of the user. Required.
        :paramtype user_id: str
        :keyword is_anonymous: True if the Microsoft Teams user is anonymous. By default false if
         missing.
        :paramtype is_anonymous: bool
        :keyword cloud: The cloud that the Microsoft Teams user belongs to. By default 'public' if
         missing. Known values are: "public", "dod", and "gcch".
        :paramtype cloud: str or
         ~azure.communication.callautomation.models.CommunicationCloudEnvironmentModel
        """
        super().__init__(**kwargs)
        self.user_id = user_id
        self.is_anonymous = is_anonymous
        self.cloud = cloud


class MuteParticipantsRequest(_serialization.Model):
    """The request payload for muting participants from the call.

    All required parameters must be populated in order to send to server.

    :ivar target_participants: Participants to be muted from the call.
     Only ACS Users are supported. Required.
    :vartype target_participants:
     list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    """

    _validation = {
        "target_participants": {"required": True},
    }

    _attribute_map = {
        "target_participants": {"key": "targetParticipants", "type": "[CommunicationIdentifierModel]"},
        "operation_context": {"key": "operationContext", "type": "str"},
    }

    def __init__(
        self,
        *,
        target_participants: List["_models.CommunicationIdentifierModel"],
        operation_context: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword target_participants: Participants to be muted from the call.
         Only ACS Users are supported. Required.
        :paramtype target_participants:
         list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        """
        super().__init__(**kwargs)
        self.target_participants = target_participants
        self.operation_context = operation_context


class MuteParticipantsResult(_serialization.Model):
    """The result payload for muting participants from the call.

    :ivar operation_context: The operation context provided by client.
    :vartype operation_context: str
    """

    _attribute_map = {
        "operation_context": {"key": "operationContext", "type": "str"},
    }

    def __init__(self, *, operation_context: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword operation_context: The operation context provided by client.
        :paramtype operation_context: str
        """
        super().__init__(**kwargs)
        self.operation_context = operation_context


class ParticipantsUpdated(_serialization.Model):
    """The participants updated in a call event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar sequence_number: The Sequence Number of the event.
    :vartype sequence_number: int
    :ivar participants: The list of participants in the call.
    :vartype participants: list[~azure.communication.callautomation.models.CallParticipant]
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "sequence_number": {"key": "sequenceNumber", "type": "int"},
        "participants": {"key": "participants", "type": "[CallParticipant]"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        sequence_number: Optional[int] = None,
        participants: Optional[List["_models.CallParticipant"]] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword sequence_number: The Sequence Number of the event.
        :paramtype sequence_number: int
        :keyword participants: The list of participants in the call.
        :paramtype participants: list[~azure.communication.callautomation.models.CallParticipant]
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.sequence_number = sequence_number
        self.participants = participants
        self.result_information = result_information


class PhoneNumberIdentifierModel(_serialization.Model):
    """A phone number.

    All required parameters must be populated in order to send to server.

    :ivar value: The phone number in E.164 format. Required.
    :vartype value: str
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "str"},
    }

    def __init__(self, *, value: str, **kwargs: Any) -> None:
        """
        :keyword value: The phone number in E.164 format. Required.
        :paramtype value: str
        """
        super().__init__(**kwargs)
        self.value = value


class PlayCanceled(_serialization.Model):
    """PlayCanceled.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class PlayCompleted(_serialization.Model):
    """PlayCompleted.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class PlayFailed(_serialization.Model):
    """PlayFailed.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar failed_play_source_index: Contains the index of the failed play source.
    :vartype failed_play_source_index: int
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "failed_play_source_index": {"key": "failedPlaySourceIndex", "type": "int"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        failed_play_source_index: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword failed_play_source_index: Contains the index of the failed play source.
        :paramtype failed_play_source_index: int
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information
        self.failed_play_source_index = failed_play_source_index


class PlayOptions(_serialization.Model):
    """PlayOptions.

    All required parameters must be populated in order to send to server.

    :ivar loop: The option to play the provided audio source in loop when set to true. Required.
    :vartype loop: bool
    """

    _validation = {
        "loop": {"required": True},
    }

    _attribute_map = {
        "loop": {"key": "loop", "type": "bool"},
    }

    def __init__(self, *, loop: bool, **kwargs: Any) -> None:
        """
        :keyword loop: The option to play the provided audio source in loop when set to true. Required.
        :paramtype loop: bool
        """
        super().__init__(**kwargs)
        self.loop = loop


class PlayRequest(_serialization.Model):
    """PlayRequest.

    All required parameters must be populated in order to send to server.

    :ivar play_sources: The source of the audio to be played. Required.
    :vartype play_sources: list[~azure.communication.callautomation.models.PlaySource]
    :ivar play_to: The list of call participants play provided audio to.
     Plays to everyone in the call when not provided.
    :vartype play_to: list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
    :ivar interrupt_call_media_operation: If set play can barge into other existing
     queued-up/currently-processing requests.
    :vartype interrupt_call_media_operation: bool
    :ivar play_options: Defines options for playing the audio.
    :vartype play_options: ~azure.communication.callautomation.models.PlayOptions
    :ivar operation_context: The value to identify context of the operation.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _validation = {
        "play_sources": {"required": True},
    }

    _attribute_map = {
        "play_sources": {"key": "playSources", "type": "[PlaySource]"},
        "play_to": {"key": "playTo", "type": "[CommunicationIdentifierModel]"},
        "interrupt_call_media_operation": {"key": "interruptCallMediaOperation", "type": "bool"},
        "play_options": {"key": "playOptions", "type": "PlayOptions"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        play_sources: List["_models.PlaySource"],
        play_to: Optional[List["_models.CommunicationIdentifierModel"]] = None,
        interrupt_call_media_operation: Optional[bool] = None,
        play_options: Optional["_models.PlayOptions"] = None,
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword play_sources: The source of the audio to be played. Required.
        :paramtype play_sources: list[~azure.communication.callautomation.models.PlaySource]
        :keyword play_to: The list of call participants play provided audio to.
         Plays to everyone in the call when not provided.
        :paramtype play_to:
         list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
        :keyword interrupt_call_media_operation: If set play can barge into other existing
         queued-up/currently-processing requests.
        :paramtype interrupt_call_media_operation: bool
        :keyword play_options: Defines options for playing the audio.
        :paramtype play_options: ~azure.communication.callautomation.models.PlayOptions
        :keyword operation_context: The value to identify context of the operation.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.play_sources = play_sources
        self.play_to = play_to
        self.interrupt_call_media_operation = interrupt_call_media_operation
        self.play_options = play_options
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class PlaySource(_serialization.Model):
    """PlaySource.

    All required parameters must be populated in order to send to server.

    :ivar kind: Defines the type of the play source. Required. Known values are: "file", "text",
     and "ssml".
    :vartype kind: str or ~azure.communication.callautomation.models.PlaySourceType
    :ivar play_source_cache_id: Defines the identifier to be used for caching related media.
    :vartype play_source_cache_id: str
    :ivar file: Defines the file source info to be used for play.
    :vartype file: ~azure.communication.callautomation.models.FileSource
    :ivar text: Defines the text source info to be used for play.
    :vartype text: ~azure.communication.callautomation.models.TextSource
    :ivar ssml: Defines the ssml(Speech Synthesis Markup Language) source info to be used for play.
    :vartype ssml: ~azure.communication.callautomation.models.SsmlSource
    """

    _validation = {
        "kind": {"required": True},
    }

    _attribute_map = {
        "kind": {"key": "kind", "type": "str"},
        "play_source_cache_id": {"key": "playSourceCacheId", "type": "str"},
        "file": {"key": "file", "type": "FileSource"},
        "text": {"key": "text", "type": "TextSource"},
        "ssml": {"key": "ssml", "type": "SsmlSource"},
    }

    def __init__(
        self,
        *,
        kind: Union[str, "_models.PlaySourceType"],
        play_source_cache_id: Optional[str] = None,
        file: Optional["_models.FileSource"] = None,
        text: Optional["_models.TextSource"] = None,
        ssml: Optional["_models.SsmlSource"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword kind: Defines the type of the play source. Required. Known values are: "file", "text",
         and "ssml".
        :paramtype kind: str or ~azure.communication.callautomation.models.PlaySourceType
        :keyword play_source_cache_id: Defines the identifier to be used for caching related media.
        :paramtype play_source_cache_id: str
        :keyword file: Defines the file source info to be used for play.
        :paramtype file: ~azure.communication.callautomation.models.FileSource
        :keyword text: Defines the text source info to be used for play.
        :paramtype text: ~azure.communication.callautomation.models.TextSource
        :keyword ssml: Defines the ssml(Speech Synthesis Markup Language) source info to be used for
         play.
        :paramtype ssml: ~azure.communication.callautomation.models.SsmlSource
        """
        super().__init__(**kwargs)
        self.kind = kind
        self.play_source_cache_id = play_source_cache_id
        self.file = file
        self.text = text
        self.ssml = ssml


class PlayStarted(_serialization.Model):
    """Play started event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class RecognizeCanceled(_serialization.Model):
    """RecognizeCanceled.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class RecognizeCompleted(_serialization.Model):
    """RecognizeCompleted.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar recognition_type: Determines the sub-type of the recognize operation.
     In case of cancel operation the this field is not set and is returned empty. Known values are:
     "dtmf", "speech", and "choices".
    :vartype recognition_type: str or ~azure.communication.callautomation.models.RecognitionType
    :ivar dtmf_result: Defines the result for RecognitionType = Dtmf.
    :vartype dtmf_result: ~azure.communication.callautomation.models.DtmfResult
    :ivar choice_result: Defines the result for RecognitionType = Choices.
    :vartype choice_result: ~azure.communication.callautomation.models.ChoiceResult
    :ivar speech_result: Defines the result for RecognitionType = Speech and SpeechOrDtmf.
    :vartype speech_result: ~azure.communication.callautomation.models.SpeechResult
    """

    _validation = {
        "speech_result": {"readonly": True},
    }

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "recognition_type": {"key": "recognitionType", "type": "str"},
        "dtmf_result": {"key": "dtmfResult", "type": "DtmfResult"},
        "choice_result": {"key": "choiceResult", "type": "ChoiceResult"},
        "speech_result": {"key": "speechResult", "type": "SpeechResult"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        recognition_type: Optional[Union[str, "_models.RecognitionType"]] = None,
        dtmf_result: Optional["_models.DtmfResult"] = None,
        choice_result: Optional["_models.ChoiceResult"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword recognition_type: Determines the sub-type of the recognize operation.
         In case of cancel operation the this field is not set and is returned empty. Known values are:
         "dtmf", "speech", and "choices".
        :paramtype recognition_type: str or ~azure.communication.callautomation.models.RecognitionType
        :keyword dtmf_result: Defines the result for RecognitionType = Dtmf.
        :paramtype dtmf_result: ~azure.communication.callautomation.models.DtmfResult
        :keyword choice_result: Defines the result for RecognitionType = Choices.
        :paramtype choice_result: ~azure.communication.callautomation.models.ChoiceResult
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information
        self.recognition_type = recognition_type
        self.dtmf_result = dtmf_result
        self.choice_result = choice_result
        self.speech_result: Optional["_models.SpeechResult"] = None


class RecognizeFailed(_serialization.Model):
    """RecognizeFailed.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar failed_play_source_index: Contains the index of the failed play source.
    :vartype failed_play_source_index: int
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "failed_play_source_index": {"key": "failedPlaySourceIndex", "type": "int"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        failed_play_source_index: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword failed_play_source_index: Contains the index of the failed play source.
        :paramtype failed_play_source_index: int
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information
        self.failed_play_source_index = failed_play_source_index


class RecognizeOptions(_serialization.Model):
    """RecognizeOptions.

    All required parameters must be populated in order to send to server.

    :ivar interrupt_prompt: Determines if we interrupt the prompt and start recognizing.
    :vartype interrupt_prompt: bool
    :ivar initial_silence_timeout_in_seconds: Time to wait for first input after prompt (if any).
    :vartype initial_silence_timeout_in_seconds: int
    :ivar target_participant: Target participant of DTMF tone recognition. Required.
    :vartype target_participant:
     ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar speech_language: Speech language to be recognized, If not set default is en-US.
    :vartype speech_language: str
    :ivar speech_recognition_model_endpoint_id: Endpoint where the custom model was deployed.
    :vartype speech_recognition_model_endpoint_id: str
    :ivar dtmf_options: Defines configurations for DTMF.
    :vartype dtmf_options: ~azure.communication.callautomation.models.DtmfOptions
    :ivar choices: Defines Ivr choices for recognize.
    :vartype choices: list[~azure.communication.callautomation.models.Choice]
    :ivar speech_options: Defines continuous speech recognition option.
    :vartype speech_options: ~azure.communication.callautomation.models.SpeechOptions
    """

    _validation = {
        "initial_silence_timeout_in_seconds": {"maximum": 300, "minimum": 0},
        "target_participant": {"required": True},
    }

    _attribute_map = {
        "interrupt_prompt": {"key": "interruptPrompt", "type": "bool"},
        "initial_silence_timeout_in_seconds": {"key": "initialSilenceTimeoutInSeconds", "type": "int"},
        "target_participant": {"key": "targetParticipant", "type": "CommunicationIdentifierModel"},
        "speech_language": {"key": "speechLanguage", "type": "str"},
        "speech_recognition_model_endpoint_id": {"key": "speechRecognitionModelEndpointId", "type": "str"},
        "dtmf_options": {"key": "dtmfOptions", "type": "DtmfOptions"},
        "choices": {"key": "choices", "type": "[Choice]"},
        "speech_options": {"key": "speechOptions", "type": "SpeechOptions"},
    }

    def __init__(
        self,
        *,
        target_participant: "_models.CommunicationIdentifierModel",
        interrupt_prompt: Optional[bool] = None,
        initial_silence_timeout_in_seconds: Optional[int] = None,
        speech_language: Optional[str] = None,
        speech_recognition_model_endpoint_id: Optional[str] = None,
        dtmf_options: Optional["_models.DtmfOptions"] = None,
        choices: Optional[List["_models.Choice"]] = None,
        speech_options: Optional["_models.SpeechOptions"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword interrupt_prompt: Determines if we interrupt the prompt and start recognizing.
        :paramtype interrupt_prompt: bool
        :keyword initial_silence_timeout_in_seconds: Time to wait for first input after prompt (if
         any).
        :paramtype initial_silence_timeout_in_seconds: int
        :keyword target_participant: Target participant of DTMF tone recognition. Required.
        :paramtype target_participant:
         ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword speech_language: Speech language to be recognized, If not set default is en-US.
        :paramtype speech_language: str
        :keyword speech_recognition_model_endpoint_id: Endpoint where the custom model was deployed.
        :paramtype speech_recognition_model_endpoint_id: str
        :keyword dtmf_options: Defines configurations for DTMF.
        :paramtype dtmf_options: ~azure.communication.callautomation.models.DtmfOptions
        :keyword choices: Defines Ivr choices for recognize.
        :paramtype choices: list[~azure.communication.callautomation.models.Choice]
        :keyword speech_options: Defines continuous speech recognition option.
        :paramtype speech_options: ~azure.communication.callautomation.models.SpeechOptions
        """
        super().__init__(**kwargs)
        self.interrupt_prompt = interrupt_prompt
        self.initial_silence_timeout_in_seconds = initial_silence_timeout_in_seconds
        self.target_participant = target_participant
        self.speech_language = speech_language
        self.speech_recognition_model_endpoint_id = speech_recognition_model_endpoint_id
        self.dtmf_options = dtmf_options
        self.choices = choices
        self.speech_options = speech_options


class RecognizeRequest(_serialization.Model):
    """RecognizeRequest.

    All required parameters must be populated in order to send to server.

    :ivar recognize_input_type: Determines the type of the recognition. Required. Known values are:
     "dtmf", "speech", "speechOrDtmf", and "choices".
    :vartype recognize_input_type: str or
     ~azure.communication.callautomation.models.RecognizeInputType
    :ivar play_prompt: The source of the audio to be played for recognition.
    :vartype play_prompt: ~azure.communication.callautomation.models.PlaySource
    :ivar play_prompts: The source of the audio to be played for recognition.
    :vartype play_prompts: list[~azure.communication.callautomation.models.PlaySource]
    :ivar interrupt_call_media_operation: If set recognize can barge into other existing
     queued-up/currently-processing requests.
    :vartype interrupt_call_media_operation: bool
    :ivar recognize_options: Defines options for recognition. Required.
    :vartype recognize_options: ~azure.communication.callautomation.models.RecognizeOptions
    :ivar operation_context: The value to identify context of the operation.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _validation = {
        "recognize_input_type": {"required": True},
        "recognize_options": {"required": True},
    }

    _attribute_map = {
        "recognize_input_type": {"key": "recognizeInputType", "type": "str"},
        "play_prompt": {"key": "playPrompt", "type": "PlaySource"},
        "play_prompts": {"key": "playPrompts", "type": "[PlaySource]"},
        "interrupt_call_media_operation": {"key": "interruptCallMediaOperation", "type": "bool"},
        "recognize_options": {"key": "recognizeOptions", "type": "RecognizeOptions"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        recognize_input_type: Union[str, "_models.RecognizeInputType"],
        recognize_options: "_models.RecognizeOptions",
        play_prompt: Optional["_models.PlaySource"] = None,
        play_prompts: Optional[List["_models.PlaySource"]] = None,
        interrupt_call_media_operation: Optional[bool] = None,
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword recognize_input_type: Determines the type of the recognition. Required. Known values
         are: "dtmf", "speech", "speechOrDtmf", and "choices".
        :paramtype recognize_input_type: str or
         ~azure.communication.callautomation.models.RecognizeInputType
        :keyword play_prompt: The source of the audio to be played for recognition.
        :paramtype play_prompt: ~azure.communication.callautomation.models.PlaySource
        :keyword play_prompts: The source of the audio to be played for recognition.
        :paramtype play_prompts: list[~azure.communication.callautomation.models.PlaySource]
        :keyword interrupt_call_media_operation: If set recognize can barge into other existing
         queued-up/currently-processing requests.
        :paramtype interrupt_call_media_operation: bool
        :keyword recognize_options: Defines options for recognition. Required.
        :paramtype recognize_options: ~azure.communication.callautomation.models.RecognizeOptions
        :keyword operation_context: The value to identify context of the operation.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.recognize_input_type = recognize_input_type
        self.play_prompt = play_prompt
        self.play_prompts = play_prompts
        self.interrupt_call_media_operation = interrupt_call_media_operation
        self.recognize_options = recognize_options
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class RecordingStateChanged(_serialization.Model):
    """RecordingStateChanged.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar recording_id: The call recording id.
    :vartype recording_id: str
    :ivar state: Known values are: "active" and "inactive".
    :vartype state: str or ~azure.communication.callautomation.models.RecordingState
    :ivar start_date_time: The time of the recording started.
    :vartype start_date_time: ~datetime.datetime
    :ivar recording_kind: Known values are: "AzureCommunicationServices", "Teams", and
     "TeamsCompliance".
    :vartype recording_kind: str or ~azure.communication.callautomation.models.RecordingKind
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _validation = {
        "recording_id": {"readonly": True},
        "start_date_time": {"readonly": True},
    }

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "recording_id": {"key": "recordingId", "type": "str"},
        "state": {"key": "state", "type": "str"},
        "start_date_time": {"key": "startDateTime", "type": "iso-8601"},
        "recording_kind": {"key": "recordingKind", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        state: Optional[Union[str, "_models.RecordingState"]] = None,
        recording_kind: Optional[Union[str, "_models.RecordingKind"]] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword state: Known values are: "active" and "inactive".
        :paramtype state: str or ~azure.communication.callautomation.models.RecordingState
        :keyword recording_kind: Known values are: "AzureCommunicationServices", "Teams", and
         "TeamsCompliance".
        :paramtype recording_kind: str or ~azure.communication.callautomation.models.RecordingKind
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.recording_id: Optional[str] = None
        self.state = state
        self.start_date_time: Optional[datetime.datetime] = None
        self.recording_kind = recording_kind
        self.result_information = result_information


class RecordingStateResponse(_serialization.Model):
    """RecordingStateResponse.

    :ivar recording_id:
    :vartype recording_id: str
    :ivar recording_state: Known values are: "active" and "inactive".
    :vartype recording_state: str or ~azure.communication.callautomation.models.RecordingState
    :ivar recording_kind: Known values are: "AzureCommunicationServices", "Teams", and
     "TeamsCompliance".
    :vartype recording_kind: str or ~azure.communication.callautomation.models.RecordingKind
    """

    _attribute_map = {
        "recording_id": {"key": "recordingId", "type": "str"},
        "recording_state": {"key": "recordingState", "type": "str"},
        "recording_kind": {"key": "recordingKind", "type": "str"},
    }

    def __init__(
        self,
        *,
        recording_id: Optional[str] = None,
        recording_state: Optional[Union[str, "_models.RecordingState"]] = None,
        recording_kind: Optional[Union[str, "_models.RecordingKind"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword recording_id:
        :paramtype recording_id: str
        :keyword recording_state: Known values are: "active" and "inactive".
        :paramtype recording_state: str or ~azure.communication.callautomation.models.RecordingState
        :keyword recording_kind: Known values are: "AzureCommunicationServices", "Teams", and
         "TeamsCompliance".
        :paramtype recording_kind: str or ~azure.communication.callautomation.models.RecordingKind
        """
        super().__init__(**kwargs)
        self.recording_id = recording_id
        self.recording_state = recording_state
        self.recording_kind = recording_kind


class RedirectCallRequest(_serialization.Model):
    """The request payload for redirecting the call.

    All required parameters must be populated in order to send to server.

    :ivar incoming_call_context: The context associated with the call. Required.
    :vartype incoming_call_context: str
    :ivar target: The target identity to redirect the call to. Required.
    :vartype target: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    """

    _validation = {
        "incoming_call_context": {"required": True},
        "target": {"required": True},
    }

    _attribute_map = {
        "incoming_call_context": {"key": "incomingCallContext", "type": "str"},
        "target": {"key": "target", "type": "CommunicationIdentifierModel"},
    }

    def __init__(
        self, *, incoming_call_context: str, target: "_models.CommunicationIdentifierModel", **kwargs: Any
    ) -> None:
        """
        :keyword incoming_call_context: The context associated with the call. Required.
        :paramtype incoming_call_context: str
        :keyword target: The target identity to redirect the call to. Required.
        :paramtype target: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        """
        super().__init__(**kwargs)
        self.incoming_call_context = incoming_call_context
        self.target = target


class RejectCallRequest(_serialization.Model):
    """The request payload for rejecting the call.

    All required parameters must be populated in order to send to server.

    :ivar incoming_call_context: The context associated with the call. Required.
    :vartype incoming_call_context: str
    :ivar call_reject_reason: The rejection reason. Known values are: "none", "busy", and
     "forbidden".
    :vartype call_reject_reason: str or ~azure.communication.callautomation.models.CallRejectReason
    """

    _validation = {
        "incoming_call_context": {"required": True},
    }

    _attribute_map = {
        "incoming_call_context": {"key": "incomingCallContext", "type": "str"},
        "call_reject_reason": {"key": "callRejectReason", "type": "str"},
    }

    def __init__(
        self,
        *,
        incoming_call_context: str,
        call_reject_reason: Optional[Union[str, "_models.CallRejectReason"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword incoming_call_context: The context associated with the call. Required.
        :paramtype incoming_call_context: str
        :keyword call_reject_reason: The rejection reason. Known values are: "none", "busy", and
         "forbidden".
        :paramtype call_reject_reason: str or
         ~azure.communication.callautomation.models.CallRejectReason
        """
        super().__init__(**kwargs)
        self.incoming_call_context = incoming_call_context
        self.call_reject_reason = call_reject_reason


class RemoveParticipantFailed(_serialization.Model):
    """The failed to remove participant event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar participant: Participant.
    :vartype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "participant": {"key": "participant", "type": "CommunicationIdentifierModel"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        participant: Optional["_models.CommunicationIdentifierModel"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword participant: Participant.
        :paramtype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information
        self.participant = participant


class RemoveParticipantRequest(_serialization.Model):
    """The remove participant by identifier request.

    All required parameters must be populated in order to send to server.

    :ivar participant_to_remove: The participants to be removed from the call. Required.
    :vartype participant_to_remove:
     ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _validation = {
        "participant_to_remove": {"required": True},
    }

    _attribute_map = {
        "participant_to_remove": {"key": "participantToRemove", "type": "CommunicationIdentifierModel"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        participant_to_remove: "_models.CommunicationIdentifierModel",
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword participant_to_remove: The participants to be removed from the call. Required.
        :paramtype participant_to_remove:
         ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.participant_to_remove = participant_to_remove
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class RemoveParticipantResponse(_serialization.Model):
    """The response payload for removing participants of the call.

    :ivar operation_context: The operation context provided by client.
    :vartype operation_context: str
    """

    _attribute_map = {
        "operation_context": {"key": "operationContext", "type": "str"},
    }

    def __init__(self, *, operation_context: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword operation_context: The operation context provided by client.
        :paramtype operation_context: str
        """
        super().__init__(**kwargs)
        self.operation_context = operation_context


class RemoveParticipantSucceeded(_serialization.Model):
    """The participant removed event.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    :ivar participant: Participant.
    :vartype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
        "participant": {"key": "participant", "type": "CommunicationIdentifierModel"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        participant: Optional["_models.CommunicationIdentifierModel"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        :keyword participant: Participant.
        :paramtype participant: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information
        self.participant = participant


class ResultInformation(_serialization.Model):
    """ResultInformation.

    :ivar code: Code of the current result. This can be helpful to Call Automation team to
     troubleshoot the issue if this result was unexpected.
    :vartype code: int
    :ivar sub_code: Subcode of the current result. This can be helpful to Call Automation team to
     troubleshoot the issue if this result was unexpected.
    :vartype sub_code: int
    :ivar message: Detail message that describes the current result.
    :vartype message: str
    """

    _attribute_map = {
        "code": {"key": "code", "type": "int"},
        "sub_code": {"key": "subCode", "type": "int"},
        "message": {"key": "message", "type": "str"},
    }

    def __init__(
        self,
        *,
        code: Optional[int] = None,
        sub_code: Optional[int] = None,
        message: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword code: Code of the current result. This can be helpful to Call Automation team to
         troubleshoot the issue if this result was unexpected.
        :paramtype code: int
        :keyword sub_code: Subcode of the current result. This can be helpful to Call Automation team
         to troubleshoot the issue if this result was unexpected.
        :paramtype sub_code: int
        :keyword message: Detail message that describes the current result.
        :paramtype message: str
        """
        super().__init__(**kwargs)
        self.code = code
        self.sub_code = sub_code
        self.message = message


class SendDtmfTonesCompleted(_serialization.Model):
    """SendDtmfTonesCompleted.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class SendDtmfTonesFailed(_serialization.Model):
    """SendDtmfTonesFailed.

    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _attribute_map = {
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class SendDtmfTonesRequest(_serialization.Model):
    """SendDtmfTonesRequest.

    All required parameters must be populated in order to send to server.

    :ivar tones: List of tones to be sent to target participant. Required.
    :vartype tones: list[str or ~azure.communication.callautomation.models.DtmfTone]
    :ivar target_participant: Target participant of send Dtmf tones. Required.
    :vartype target_participant:
     ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar operation_context: The value to identify context of the operation.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _validation = {
        "tones": {"required": True},
        "target_participant": {"required": True},
    }

    _attribute_map = {
        "tones": {"key": "tones", "type": "[str]"},
        "target_participant": {"key": "targetParticipant", "type": "CommunicationIdentifierModel"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        tones: List[Union[str, "_models.DtmfTone"]],
        target_participant: "_models.CommunicationIdentifierModel",
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tones: List of tones to be sent to target participant. Required.
        :paramtype tones: list[str or ~azure.communication.callautomation.models.DtmfTone]
        :keyword target_participant: Target participant of send Dtmf tones. Required.
        :paramtype target_participant:
         ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword operation_context: The value to identify context of the operation.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.tones = tones
        self.target_participant = target_participant
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class SendDtmfTonesResult(_serialization.Model):
    """SendDtmfTonesResult.

    :ivar operation_context: The operation context provided by client.
    :vartype operation_context: str
    """

    _attribute_map = {
        "operation_context": {"key": "operationContext", "type": "str"},
    }

    def __init__(self, *, operation_context: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword operation_context: The operation context provided by client.
        :paramtype operation_context: str
        """
        super().__init__(**kwargs)
        self.operation_context = operation_context


class SpeechOptions(_serialization.Model):
    """Options for continuous speech recognition.

    :ivar end_silence_timeout_in_ms: The length of end silence when user stops speaking and
     cogservice send response.
    :vartype end_silence_timeout_in_ms: int
    """

    _attribute_map = {
        "end_silence_timeout_in_ms": {"key": "endSilenceTimeoutInMs", "type": "int"},
    }

    def __init__(self, *, end_silence_timeout_in_ms: Optional[int] = None, **kwargs: Any) -> None:
        """
        :keyword end_silence_timeout_in_ms: The length of end silence when user stops speaking and
         cogservice send response.
        :paramtype end_silence_timeout_in_ms: int
        """
        super().__init__(**kwargs)
        self.end_silence_timeout_in_ms = end_silence_timeout_in_ms


class SpeechResult(_serialization.Model):
    """The speech status as a result.

    :ivar speech: The recognized speech in string.
    :vartype speech: str
    :ivar confidence: The confidence level of the recognized speech, if available, ranges from 0.0
     to 1.0.
    :vartype confidence: float
    """

    _attribute_map = {
        "speech": {"key": "speech", "type": "str"},
        "confidence": {"key": "confidence", "type": "float"},
    }

    def __init__(self, *, speech: Optional[str] = None, confidence: Optional[float] = None, **kwargs: Any) -> None:
        """
        :keyword speech: The recognized speech in string.
        :paramtype speech: str
        :keyword confidence: The confidence level of the recognized speech, if available, ranges from
         0.0 to 1.0.
        :paramtype confidence: float
        """
        super().__init__(**kwargs)
        self.speech = speech
        self.confidence = confidence


class SsmlSource(_serialization.Model):
    """SsmlSource.

    All required parameters must be populated in order to send to server.

    :ivar ssml_text: Ssml string for the cognitive service to be played. Required.
    :vartype ssml_text: str
    :ivar custom_voice_endpoint_id: Endpoint where the custom voice was deployed.
    :vartype custom_voice_endpoint_id: str
    """

    _validation = {
        "ssml_text": {"required": True},
    }

    _attribute_map = {
        "ssml_text": {"key": "ssmlText", "type": "str"},
        "custom_voice_endpoint_id": {"key": "customVoiceEndpointId", "type": "str"},
    }

    def __init__(self, *, ssml_text: str, custom_voice_endpoint_id: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword ssml_text: Ssml string for the cognitive service to be played. Required.
        :paramtype ssml_text: str
        :keyword custom_voice_endpoint_id: Endpoint where the custom voice was deployed.
        :paramtype custom_voice_endpoint_id: str
        """
        super().__init__(**kwargs)
        self.ssml_text = ssml_text
        self.custom_voice_endpoint_id = custom_voice_endpoint_id


class StartCallRecordingRequest(_serialization.Model):
    """The request payload start for call recording operation with call locator.

    All required parameters must be populated in order to send to server.

    :ivar call_locator: The call locator. Required.
    :vartype call_locator: ~azure.communication.callautomation.models.CallLocator
    :ivar recording_state_callback_uri: The uri to send notifications to.
    :vartype recording_state_callback_uri: str
    :ivar recording_content_type: The content type of call recording. Known values are: "audio" and
     "audioVideo".
    :vartype recording_content_type: str or
     ~azure.communication.callautomation.models.RecordingContent
    :ivar recording_channel_type: The channel type of call recording. Known values are: "mixed" and
     "unmixed".
    :vartype recording_channel_type: str or
     ~azure.communication.callautomation.models.RecordingChannel
    :ivar recording_format_type: The format type of call recording. Known values are: "wav", "mp3",
     and "mp4".
    :vartype recording_format_type: str or
     ~azure.communication.callautomation.models.RecordingFormat
    :ivar audio_channel_participant_ordering: The sequential order in which audio channels are
     assigned to participants in the unmixed recording.
     When 'recordingChannelType' is set to 'unmixed' and `audioChannelParticipantOrdering is not
     specified,
     the audio channel to participant mapping will be automatically assigned based on the order in
     which participant
     first audio was detected.  Channel to participant mapping details can be found in the metadata
     of the recording.
    :vartype audio_channel_participant_ordering:
     list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
    :ivar channel_affinity: The channel affinity of call recording
     When 'recordingChannelType' is set to 'unmixed', if channelAffinity is not specified, 'channel'
     will be automatically assigned.
     Channel-Participant mapping details can be found in the metadata of the recording.
     ///.
    :vartype channel_affinity: list[~azure.communication.callautomation.models.ChannelAffinity]
    :ivar pause_on_start: When set to true will start recording in Pause mode, which can be
     resumed.
    :vartype pause_on_start: bool
    :ivar external_storage: Optional property to specify location where recording will be stored.
    :vartype external_storage: ~azure.communication.callautomation.models.ExternalStorage
    """

    _validation = {
        "call_locator": {"required": True},
    }

    _attribute_map = {
        "call_locator": {"key": "callLocator", "type": "CallLocator"},
        "recording_state_callback_uri": {"key": "recordingStateCallbackUri", "type": "str"},
        "recording_content_type": {"key": "recordingContentType", "type": "str"},
        "recording_channel_type": {"key": "recordingChannelType", "type": "str"},
        "recording_format_type": {"key": "recordingFormatType", "type": "str"},
        "audio_channel_participant_ordering": {
            "key": "audioChannelParticipantOrdering",
            "type": "[CommunicationIdentifierModel]",
        },
        "channel_affinity": {"key": "channelAffinity", "type": "[ChannelAffinity]"},
        "pause_on_start": {"key": "pauseOnStart", "type": "bool"},
        "external_storage": {"key": "externalStorage", "type": "ExternalStorage"},
    }

    def __init__(
        self,
        *,
        call_locator: "_models.CallLocator",
        recording_state_callback_uri: Optional[str] = None,
        recording_content_type: Optional[Union[str, "_models.RecordingContent"]] = None,
        recording_channel_type: Optional[Union[str, "_models.RecordingChannel"]] = None,
        recording_format_type: Optional[Union[str, "_models.RecordingFormat"]] = None,
        audio_channel_participant_ordering: Optional[List["_models.CommunicationIdentifierModel"]] = None,
        channel_affinity: Optional[List["_models.ChannelAffinity"]] = None,
        pause_on_start: Optional[bool] = None,
        external_storage: Optional["_models.ExternalStorage"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_locator: The call locator. Required.
        :paramtype call_locator: ~azure.communication.callautomation.models.CallLocator
        :keyword recording_state_callback_uri: The uri to send notifications to.
        :paramtype recording_state_callback_uri: str
        :keyword recording_content_type: The content type of call recording. Known values are: "audio"
         and "audioVideo".
        :paramtype recording_content_type: str or
         ~azure.communication.callautomation.models.RecordingContent
        :keyword recording_channel_type: The channel type of call recording. Known values are: "mixed"
         and "unmixed".
        :paramtype recording_channel_type: str or
         ~azure.communication.callautomation.models.RecordingChannel
        :keyword recording_format_type: The format type of call recording. Known values are: "wav",
         "mp3", and "mp4".
        :paramtype recording_format_type: str or
         ~azure.communication.callautomation.models.RecordingFormat
        :keyword audio_channel_participant_ordering: The sequential order in which audio channels are
         assigned to participants in the unmixed recording.
         When 'recordingChannelType' is set to 'unmixed' and `audioChannelParticipantOrdering is not
         specified,
         the audio channel to participant mapping will be automatically assigned based on the order in
         which participant
         first audio was detected.  Channel to participant mapping details can be found in the metadata
         of the recording.
        :paramtype audio_channel_participant_ordering:
         list[~azure.communication.callautomation.models.CommunicationIdentifierModel]
        :keyword channel_affinity: The channel affinity of call recording
         When 'recordingChannelType' is set to 'unmixed', if channelAffinity is not specified, 'channel'
         will be automatically assigned.
         Channel-Participant mapping details can be found in the metadata of the recording.
         ///.
        :paramtype channel_affinity: list[~azure.communication.callautomation.models.ChannelAffinity]
        :keyword pause_on_start: When set to true will start recording in Pause mode, which can be
         resumed.
        :paramtype pause_on_start: bool
        :keyword external_storage: Optional property to specify location where recording will be
         stored.
        :paramtype external_storage: ~azure.communication.callautomation.models.ExternalStorage
        """
        super().__init__(**kwargs)
        self.call_locator = call_locator
        self.recording_state_callback_uri = recording_state_callback_uri
        self.recording_content_type = recording_content_type
        self.recording_channel_type = recording_channel_type
        self.recording_format_type = recording_format_type
        self.audio_channel_participant_ordering = audio_channel_participant_ordering
        self.channel_affinity = channel_affinity
        self.pause_on_start = pause_on_start
        self.external_storage = external_storage


class StartMediaStreamingRequest(_serialization.Model):
    """StartMediaStreamingRequest.

    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    :ivar operation_context: The value to identify context of the operation.
    :vartype operation_context: str
    """

    _attribute_map = {
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
    }

    def __init__(
        self, *, operation_callback_uri: Optional[str] = None, operation_context: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        :keyword operation_context: The value to identify context of the operation.
        :paramtype operation_context: str
        """
        super().__init__(**kwargs)
        self.operation_callback_uri = operation_callback_uri
        self.operation_context = operation_context


class StartTranscriptionRequest(_serialization.Model):
    """StartTranscriptionRequest.

    :ivar locale: Specifies the Locale used for transcription, e.g., en-CA or en-AU.
    :vartype locale: str
    :ivar speech_model_endpoint_id: The ID of the deployed custom model in GUID format. The GUID is
     generated by Azure Speech Studio, e.g., a259c255-1cdw-4ed7-a693-dd58563b6f6a.
    :vartype speech_model_endpoint_id: str
    :ivar operation_context: The value to identify context of the operation.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _attribute_map = {
        "locale": {"key": "locale", "type": "str"},
        "speech_model_endpoint_id": {"key": "speechModelEndpointId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        locale: Optional[str] = None,
        speech_model_endpoint_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword locale: Specifies the Locale used for transcription, e.g., en-CA or en-AU.
        :paramtype locale: str
        :keyword speech_model_endpoint_id: The ID of the deployed custom model in GUID format. The GUID
         is generated by Azure Speech Studio, e.g., a259c255-1cdw-4ed7-a693-dd58563b6f6a.
        :paramtype speech_model_endpoint_id: str
        :keyword operation_context: The value to identify context of the operation.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.locale = locale
        self.speech_model_endpoint_id = speech_model_endpoint_id
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class StopMediaStreamingRequest(_serialization.Model):
    """StopMediaStreamingRequest.

    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    :ivar operation_context: The value to identify context of the operation.
    :vartype operation_context: str
    """

    _attribute_map = {
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
    }

    def __init__(
        self, *, operation_callback_uri: Optional[str] = None, operation_context: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        :keyword operation_context: The value to identify context of the operation.
        :paramtype operation_context: str
        """
        super().__init__(**kwargs)
        self.operation_callback_uri = operation_callback_uri
        self.operation_context = operation_context


class StopTranscriptionRequest(_serialization.Model):
    """StopTranscriptionRequest.

    :ivar operation_context: The value to identify context of the operation.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _attribute_map = {
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self, *, operation_context: Optional[str] = None, operation_callback_uri: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword operation_context: The value to identify context of the operation.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class TextSource(_serialization.Model):
    """TextSource.

    All required parameters must be populated in order to send to server.

    :ivar text: Text for the cognitive service to be played. Required.
    :vartype text: str
    :ivar source_locale: Source language locale to be played
     Refer to available locales here: :code:`<seealso
     href="https://learn.microsoft.com/en-us/azure/cognitive-services/speech-service/language-support?tabs=stt-tts"
     />`.
    :vartype source_locale: str
    :ivar voice_kind: Voice kind type. Known values are: "male" and "female".
    :vartype voice_kind: str or ~azure.communication.callautomation.models.VoiceKind
    :ivar voice_name: Voice name to be played
     Refer to available Text-to-speech voices here: :code:`<seealso
     href="https://learn.microsoft.com/en-us/azure/cognitive-services/speech-service/language-support?tabs=stt-tts"
     />`.
    :vartype voice_name: str
    :ivar custom_voice_endpoint_id: Endpoint where the custom voice was deployed.
    :vartype custom_voice_endpoint_id: str
    """

    _validation = {
        "text": {"required": True},
    }

    _attribute_map = {
        "text": {"key": "text", "type": "str"},
        "source_locale": {"key": "sourceLocale", "type": "str"},
        "voice_kind": {"key": "voiceKind", "type": "str"},
        "voice_name": {"key": "voiceName", "type": "str"},
        "custom_voice_endpoint_id": {"key": "customVoiceEndpointId", "type": "str"},
    }

    def __init__(
        self,
        *,
        text: str,
        source_locale: Optional[str] = None,
        voice_kind: Optional[Union[str, "_models.VoiceKind"]] = None,
        voice_name: Optional[str] = None,
        custom_voice_endpoint_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword text: Text for the cognitive service to be played. Required.
        :paramtype text: str
        :keyword source_locale: Source language locale to be played
         Refer to available locales here: :code:`<seealso
         href="https://learn.microsoft.com/en-us/azure/cognitive-services/speech-service/language-support?tabs=stt-tts"
         />`.
        :paramtype source_locale: str
        :keyword voice_kind: Voice kind type. Known values are: "male" and "female".
        :paramtype voice_kind: str or ~azure.communication.callautomation.models.VoiceKind
        :keyword voice_name: Voice name to be played
         Refer to available Text-to-speech voices here: :code:`<seealso
         href="https://learn.microsoft.com/en-us/azure/cognitive-services/speech-service/language-support?tabs=stt-tts"
         />`.
        :paramtype voice_name: str
        :keyword custom_voice_endpoint_id: Endpoint where the custom voice was deployed.
        :paramtype custom_voice_endpoint_id: str
        """
        super().__init__(**kwargs)
        self.text = text
        self.source_locale = source_locale
        self.voice_kind = voice_kind
        self.voice_name = voice_name
        self.custom_voice_endpoint_id = custom_voice_endpoint_id


class TranscriptionFailed(_serialization.Model):
    """TranscriptionFailed.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar transcription_update: Defines the result for TranscriptionUpdate with the current status
     and the details about the status.
    :vartype transcription_update: ~azure.communication.callautomation.models.TranscriptionUpdate
    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _validation = {
        "transcription_update": {"readonly": True},
    }

    _attribute_map = {
        "transcription_update": {"key": "transcriptionUpdate", "type": "TranscriptionUpdate"},
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.transcription_update: Optional["_models.TranscriptionUpdate"] = None
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class TranscriptionOptions(_serialization.Model):
    """Options for live transcription.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    WebSocketTranscriptionOptions

    All required parameters must be populated in order to send to server.

    :ivar transport_type: Defines the transport type used for streaming. Note that future values
     may be introduced that are not currently documented. Required. "websocket"
    :vartype transport_type: str or
     ~azure.communication.callautomation.models.StreamingTransportType
    :ivar locale: Specifies the Locale used for transcription, e.g., en-CA or en-AU. Required.
    :vartype locale: str
    """

    _validation = {
        "transport_type": {"required": True},
        "locale": {"required": True},
    }

    _attribute_map = {
        "transport_type": {"key": "transportType", "type": "str"},
        "locale": {"key": "locale", "type": "str"},
    }

    _subtype_map = {"transport_type": {"websocket": "WebSocketTranscriptionOptions"}}

    def __init__(self, *, locale: str, **kwargs: Any) -> None:
        """
        :keyword locale: Specifies the Locale used for transcription, e.g., en-CA or en-AU. Required.
        :paramtype locale: str
        """
        super().__init__(**kwargs)
        self.transport_type: Optional[str] = None
        self.locale = locale


class TranscriptionStarted(_serialization.Model):
    """TranscriptionStarted.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar transcription_update: Defines the result for TranscriptionUpdate with the current status
     and the details about the status.
    :vartype transcription_update: ~azure.communication.callautomation.models.TranscriptionUpdate
    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _validation = {
        "transcription_update": {"readonly": True},
    }

    _attribute_map = {
        "transcription_update": {"key": "transcriptionUpdate", "type": "TranscriptionUpdate"},
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.transcription_update: Optional["_models.TranscriptionUpdate"] = None
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class TranscriptionStopped(_serialization.Model):
    """TranscriptionStopped.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar transcription_update: Defines the result for TranscriptionUpdate with the current status
     and the details about the status.
    :vartype transcription_update: ~azure.communication.callautomation.models.TranscriptionUpdate
    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _validation = {
        "transcription_update": {"readonly": True},
    }

    _attribute_map = {
        "transcription_update": {"key": "transcriptionUpdate", "type": "TranscriptionUpdate"},
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.transcription_update: Optional["_models.TranscriptionUpdate"] = None
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class TranscriptionSubscription(_serialization.Model):
    """Transcription Subscription Object.

    :ivar id: Subscription Id.
    :vartype id: str
    :ivar state: Transcription subscription state. Known values are: "disabled", "inactive", and
     "active".
    :vartype state: str or
     ~azure.communication.callautomation.models.TranscriptionSubscriptionState
    :ivar subscribed_result_types: Subscribed transcription result types.
    :vartype subscribed_result_types: list[str or
     ~azure.communication.callautomation.models.TranscriptionResultType]
    :ivar locale: Specifies the locale used for transcription, e.g., en-CA or en-AU.
    :vartype locale: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "state": {"key": "state", "type": "str"},
        "subscribed_result_types": {"key": "subscribedResultTypes", "type": "[str]"},
        "locale": {"key": "locale", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        state: Optional[Union[str, "_models.TranscriptionSubscriptionState"]] = None,
        subscribed_result_types: Optional[List[Union[str, "_models.TranscriptionResultType"]]] = None,
        locale: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Subscription Id.
        :paramtype id: str
        :keyword state: Transcription subscription state. Known values are: "disabled", "inactive", and
         "active".
        :paramtype state: str or
         ~azure.communication.callautomation.models.TranscriptionSubscriptionState
        :keyword subscribed_result_types: Subscribed transcription result types.
        :paramtype subscribed_result_types: list[str or
         ~azure.communication.callautomation.models.TranscriptionResultType]
        :keyword locale: Specifies the locale used for transcription, e.g., en-CA or en-AU.
        :paramtype locale: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.state = state
        self.subscribed_result_types = subscribed_result_types
        self.locale = locale


class TranscriptionUpdate(_serialization.Model):
    """TranscriptionUpdate.

    :ivar transcription_status: Known values are: "transcriptionStarted", "transcriptionFailed",
     "transcriptionResumed", "transcriptionUpdated", "transcriptionStopped", and "unspecifiedError".
    :vartype transcription_status: str or
     ~azure.communication.callautomation.models.TranscriptionStatus
    :ivar transcription_status_details: Known values are: "subscriptionStarted",
     "streamConnectionReestablished", "streamConnectionUnsuccessful", "streamUrlMissing",
     "serviceShutdown", "streamConnectionInterrupted", "speechServicesConnectionError",
     "subscriptionStopped", "unspecifiedError", "authenticationFailure", "badRequest",
     "tooManyRequests", "forbidden", "serviceTimeout", and "transcriptionLocaleUpdated".
    :vartype transcription_status_details: str or
     ~azure.communication.callautomation.models.TranscriptionStatusDetails
    """

    _attribute_map = {
        "transcription_status": {"key": "transcriptionStatus", "type": "str"},
        "transcription_status_details": {"key": "transcriptionStatusDetails", "type": "str"},
    }

    def __init__(
        self,
        *,
        transcription_status: Optional[Union[str, "_models.TranscriptionStatus"]] = None,
        transcription_status_details: Optional[Union[str, "_models.TranscriptionStatusDetails"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword transcription_status: Known values are: "transcriptionStarted", "transcriptionFailed",
         "transcriptionResumed", "transcriptionUpdated", "transcriptionStopped", and "unspecifiedError".
        :paramtype transcription_status: str or
         ~azure.communication.callautomation.models.TranscriptionStatus
        :keyword transcription_status_details: Known values are: "subscriptionStarted",
         "streamConnectionReestablished", "streamConnectionUnsuccessful", "streamUrlMissing",
         "serviceShutdown", "streamConnectionInterrupted", "speechServicesConnectionError",
         "subscriptionStopped", "unspecifiedError", "authenticationFailure", "badRequest",
         "tooManyRequests", "forbidden", "serviceTimeout", and "transcriptionLocaleUpdated".
        :paramtype transcription_status_details: str or
         ~azure.communication.callautomation.models.TranscriptionStatusDetails
        """
        super().__init__(**kwargs)
        self.transcription_status = transcription_status
        self.transcription_status_details = transcription_status_details


class TranscriptionUpdated(_serialization.Model):
    """TranscriptionUpdated.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar transcription_update: Defines the result for TranscriptionUpdate with the current status
     and the details about the status.
    :vartype transcription_update: ~azure.communication.callautomation.models.TranscriptionUpdate
    :ivar call_connection_id: Call connection ID.
    :vartype call_connection_id: str
    :ivar server_call_id: Server call ID.
    :vartype server_call_id: str
    :ivar correlation_id: Correlation ID for event to call correlation. Also called ChainId for
     skype chain ID.
    :vartype correlation_id: str
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar result_information: Contains the resulting SIP code, sub-code and message.
    :vartype result_information: ~azure.communication.callautomation.models.ResultInformation
    """

    _validation = {
        "transcription_update": {"readonly": True},
    }

    _attribute_map = {
        "transcription_update": {"key": "transcriptionUpdate", "type": "TranscriptionUpdate"},
        "call_connection_id": {"key": "callConnectionId", "type": "str"},
        "server_call_id": {"key": "serverCallId", "type": "str"},
        "correlation_id": {"key": "correlationId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "result_information": {"key": "resultInformation", "type": "ResultInformation"},
    }

    def __init__(
        self,
        *,
        call_connection_id: Optional[str] = None,
        server_call_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        result_information: Optional["_models.ResultInformation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword call_connection_id: Call connection ID.
        :paramtype call_connection_id: str
        :keyword server_call_id: Server call ID.
        :paramtype server_call_id: str
        :keyword correlation_id: Correlation ID for event to call correlation. Also called ChainId for
         skype chain ID.
        :paramtype correlation_id: str
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword result_information: Contains the resulting SIP code, sub-code and message.
        :paramtype result_information: ~azure.communication.callautomation.models.ResultInformation
        """
        super().__init__(**kwargs)
        self.transcription_update: Optional["_models.TranscriptionUpdate"] = None
        self.call_connection_id = call_connection_id
        self.server_call_id = server_call_id
        self.correlation_id = correlation_id
        self.operation_context = operation_context
        self.result_information = result_information


class TransferCallResponse(_serialization.Model):
    """The response payload for transferring the call.

    :ivar operation_context: The operation context provided by client.
    :vartype operation_context: str
    """

    _attribute_map = {
        "operation_context": {"key": "operationContext", "type": "str"},
    }

    def __init__(self, *, operation_context: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword operation_context: The operation context provided by client.
        :paramtype operation_context: str
        """
        super().__init__(**kwargs)
        self.operation_context = operation_context


class TransferToParticipantRequest(_serialization.Model):
    """The request payload for transferring call to a participant.

    All required parameters must be populated in order to send to server.

    :ivar target_participant: The identity of the target where call should be transferred to.
     Required.
    :vartype target_participant:
     ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar transferee: Transferee is the participant who is transferred away.
    :vartype transferee: ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    :ivar custom_calling_context: Used by customer to send custom calling context to targets.
    :vartype custom_calling_context:
     ~azure.communication.callautomation.models.CustomCallingContext
    :ivar source_caller_id_number: The source caller Id, a phone number, that will be used as the
     transferor's caller Id when transferring a call to a Pstn target.
    :vartype source_caller_id_number:
     ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
    """

    _validation = {
        "target_participant": {"required": True},
    }

    _attribute_map = {
        "target_participant": {"key": "targetParticipant", "type": "CommunicationIdentifierModel"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "transferee": {"key": "transferee", "type": "CommunicationIdentifierModel"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
        "custom_calling_context": {"key": "customCallingContext", "type": "CustomCallingContext"},
        "source_caller_id_number": {"key": "sourceCallerIdNumber", "type": "PhoneNumberIdentifierModel"},
    }

    def __init__(
        self,
        *,
        target_participant: "_models.CommunicationIdentifierModel",
        operation_context: Optional[str] = None,
        transferee: Optional["_models.CommunicationIdentifierModel"] = None,
        operation_callback_uri: Optional[str] = None,
        custom_calling_context: Optional["_models.CustomCallingContext"] = None,
        source_caller_id_number: Optional["_models.PhoneNumberIdentifierModel"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword target_participant: The identity of the target where call should be transferred to.
         Required.
        :paramtype target_participant:
         ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword transferee: Transferee is the participant who is transferred away.
        :paramtype transferee: ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        :keyword custom_calling_context: Used by customer to send custom calling context to targets.
        :paramtype custom_calling_context:
         ~azure.communication.callautomation.models.CustomCallingContext
        :keyword source_caller_id_number: The source caller Id, a phone number, that will be used as
         the transferor's caller Id when transferring a call to a Pstn target.
        :paramtype source_caller_id_number:
         ~azure.communication.callautomation.models.PhoneNumberIdentifierModel
        """
        super().__init__(**kwargs)
        self.target_participant = target_participant
        self.operation_context = operation_context
        self.transferee = transferee
        self.operation_callback_uri = operation_callback_uri
        self.custom_calling_context = custom_calling_context
        self.source_caller_id_number = source_caller_id_number


class UnholdRequest(_serialization.Model):
    """The request payload for holding participant from the call.

    All required parameters must be populated in order to send to server.

    :ivar target_participant: Participants to be hold from the call.
     Only ACS Users are supported. Required.
    :vartype target_participant:
     ~azure.communication.callautomation.models.CommunicationIdentifierModel
    :ivar operation_context: Used by customers when calling mid-call actions to correlate the
     request to the response event.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _validation = {
        "target_participant": {"required": True},
    }

    _attribute_map = {
        "target_participant": {"key": "targetParticipant", "type": "CommunicationIdentifierModel"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        target_participant: "_models.CommunicationIdentifierModel",
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword target_participant: Participants to be hold from the call.
         Only ACS Users are supported. Required.
        :paramtype target_participant:
         ~azure.communication.callautomation.models.CommunicationIdentifierModel
        :keyword operation_context: Used by customers when calling mid-call actions to correlate the
         request to the response event.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.target_participant = target_participant
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class UpdateTranscriptionRequest(_serialization.Model):
    """UpdateTranscriptionRequest.

    :ivar locale: Specifies the Locale used for transcription, e.g., en-CA or en-AU.
    :vartype locale: str
    :ivar speech_model_endpoint_id: The ID of the deployed custom model, in GUID format, e.g.,
     a259c255-1cdw-4ed7-a693-dd58563b6f6a.
    :vartype speech_model_endpoint_id: str
    :ivar operation_context: The value to identify context of the operation.
    :vartype operation_context: str
    :ivar operation_callback_uri: Set a callback URI that overrides the default callback URI set by
     CreateCall/AnswerCall for this operation.
     This setup is per-action. If this is not set, the default callback URI set by
     CreateCall/AnswerCall will be used.
    :vartype operation_callback_uri: str
    """

    _attribute_map = {
        "locale": {"key": "locale", "type": "str"},
        "speech_model_endpoint_id": {"key": "speechModelEndpointId", "type": "str"},
        "operation_context": {"key": "operationContext", "type": "str"},
        "operation_callback_uri": {"key": "operationCallbackUri", "type": "str"},
    }

    def __init__(
        self,
        *,
        locale: Optional[str] = None,
        speech_model_endpoint_id: Optional[str] = None,
        operation_context: Optional[str] = None,
        operation_callback_uri: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword locale: Specifies the Locale used for transcription, e.g., en-CA or en-AU.
        :paramtype locale: str
        :keyword speech_model_endpoint_id: The ID of the deployed custom model, in GUID format, e.g.,
         a259c255-1cdw-4ed7-a693-dd58563b6f6a.
        :paramtype speech_model_endpoint_id: str
        :keyword operation_context: The value to identify context of the operation.
        :paramtype operation_context: str
        :keyword operation_callback_uri: Set a callback URI that overrides the default callback URI set
         by CreateCall/AnswerCall for this operation.
         This setup is per-action. If this is not set, the default callback URI set by
         CreateCall/AnswerCall will be used.
        :paramtype operation_callback_uri: str
        """
        super().__init__(**kwargs)
        self.locale = locale
        self.speech_model_endpoint_id = speech_model_endpoint_id
        self.operation_context = operation_context
        self.operation_callback_uri = operation_callback_uri


class WebSocketMediaStreamingOptions(MediaStreamingOptions):
    """Represents the options for WebSocket transport.

    All required parameters must be populated in order to send to server.

    :ivar transport_type: Defines the transport type used for streaming. Note that future values
     may be introduced that are not currently documented. Required. "websocket"
    :vartype transport_type: str or
     ~azure.communication.callautomation.models.StreamingTransportType
    :ivar audio_channel_type: The audio channel type to stream, e.g., unmixed audio, mixed audio.
     Required. Known values are: "mixed" and "unmixed".
    :vartype audio_channel_type: str or
     ~azure.communication.callautomation.models.MediaStreamingAudioChannelType
    :ivar transport_url: The transport URL for media streaming.
    :vartype transport_url: str
    :ivar content_type: "audio"
    :vartype content_type: str or
     ~azure.communication.callautomation.models.MediaStreamingContentType
    :ivar start_media_streaming: A value indicating whether the media streaming should start
     immediately after the call is answered.
    :vartype start_media_streaming: bool
    :ivar enable_bidirectional: A value indicating whether bidirectional streaming is enabled.
    :vartype enable_bidirectional: bool
    :ivar audio_format: The audio format used for encoding, including sample rate and channel type.
     The default is Pcm16KMono. Known values are: "pcm16KMono" and "pcm24KMono".
    :vartype audio_format: str or ~azure.communication.callautomation.models.AudioFormat
    :ivar enable_dtmf_tones: A value that indicates whether to stream the DTMF tones.
    :vartype enable_dtmf_tones: bool
    """

    _validation = {
        "transport_type": {"required": True},
        "audio_channel_type": {"required": True},
    }

    _attribute_map = {
        "transport_type": {"key": "transportType", "type": "str"},
        "audio_channel_type": {"key": "audioChannelType", "type": "str"},
        "transport_url": {"key": "transportUrl", "type": "str"},
        "content_type": {"key": "contentType", "type": "str"},
        "start_media_streaming": {"key": "startMediaStreaming", "type": "bool"},
        "enable_bidirectional": {"key": "enableBidirectional", "type": "bool"},
        "audio_format": {"key": "audioFormat", "type": "str"},
        "enable_dtmf_tones": {"key": "enableDtmfTones", "type": "bool"},
    }

    def __init__(
        self,
        *,
        audio_channel_type: Union[str, "_models.MediaStreamingAudioChannelType"],
        transport_url: Optional[str] = None,
        content_type: Optional[Union[str, "_models.MediaStreamingContentType"]] = None,
        start_media_streaming: Optional[bool] = None,
        enable_bidirectional: Optional[bool] = None,
        audio_format: Optional[Union[str, "_models.AudioFormat"]] = None,
        enable_dtmf_tones: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword audio_channel_type: The audio channel type to stream, e.g., unmixed audio, mixed
         audio. Required. Known values are: "mixed" and "unmixed".
        :paramtype audio_channel_type: str or
         ~azure.communication.callautomation.models.MediaStreamingAudioChannelType
        :keyword transport_url: The transport URL for media streaming.
        :paramtype transport_url: str
        :keyword content_type: "audio"
        :paramtype content_type: str or
         ~azure.communication.callautomation.models.MediaStreamingContentType
        :keyword start_media_streaming: A value indicating whether the media streaming should start
         immediately after the call is answered.
        :paramtype start_media_streaming: bool
        :keyword enable_bidirectional: A value indicating whether bidirectional streaming is enabled.
        :paramtype enable_bidirectional: bool
        :keyword audio_format: The audio format used for encoding, including sample rate and channel
         type. The default is Pcm16KMono. Known values are: "pcm16KMono" and "pcm24KMono".
        :paramtype audio_format: str or ~azure.communication.callautomation.models.AudioFormat
        :keyword enable_dtmf_tones: A value that indicates whether to stream the DTMF tones.
        :paramtype enable_dtmf_tones: bool
        """
        super().__init__(audio_channel_type=audio_channel_type, **kwargs)
        self.transport_type: str = "websocket"
        self.transport_url = transport_url
        self.content_type = content_type
        self.start_media_streaming = start_media_streaming
        self.enable_bidirectional = enable_bidirectional
        self.audio_format = audio_format
        self.enable_dtmf_tones = enable_dtmf_tones


class WebSocketTranscriptionOptions(TranscriptionOptions):
    """Represents the options for WebSocket transport.

    All required parameters must be populated in order to send to server.

    :ivar transport_type: Defines the transport type used for streaming. Note that future values
     may be introduced that are not currently documented. Required. "websocket"
    :vartype transport_type: str or
     ~azure.communication.callautomation.models.StreamingTransportType
    :ivar locale: Specifies the Locale used for transcription, e.g., en-CA or en-AU. Required.
    :vartype locale: str
    :ivar transport_url: The URL used for live transcription transport.
    :vartype transport_url: str
    :ivar speech_model_endpoint_id: The ID of the deployed custom model in GUID format. The GUID is
     generated by Azure Speech Studio, e.g., a259c255-1cdw-4ed7-a693-dd58563b6f6a.
    :vartype speech_model_endpoint_id: str
    :ivar start_transcription: Indicates whether the transcription should start immediately after
     the call is answered.
    :vartype start_transcription: bool
    :ivar enable_intermediate_results: Enables intermediate results for the transcribed speech.
    :vartype enable_intermediate_results: bool
    """

    _validation = {
        "transport_type": {"required": True},
        "locale": {"required": True},
    }

    _attribute_map = {
        "transport_type": {"key": "transportType", "type": "str"},
        "locale": {"key": "locale", "type": "str"},
        "transport_url": {"key": "transportUrl", "type": "str"},
        "speech_model_endpoint_id": {"key": "speechModelEndpointId", "type": "str"},
        "start_transcription": {"key": "startTranscription", "type": "bool"},
        "enable_intermediate_results": {"key": "enableIntermediateResults", "type": "bool"},
    }

    def __init__(
        self,
        *,
        locale: str,
        transport_url: Optional[str] = None,
        speech_model_endpoint_id: Optional[str] = None,
        start_transcription: Optional[bool] = None,
        enable_intermediate_results: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword locale: Specifies the Locale used for transcription, e.g., en-CA or en-AU. Required.
        :paramtype locale: str
        :keyword transport_url: The URL used for live transcription transport.
        :paramtype transport_url: str
        :keyword speech_model_endpoint_id: The ID of the deployed custom model in GUID format. The GUID
         is generated by Azure Speech Studio, e.g., a259c255-1cdw-4ed7-a693-dd58563b6f6a.
        :paramtype speech_model_endpoint_id: str
        :keyword start_transcription: Indicates whether the transcription should start immediately
         after the call is answered.
        :paramtype start_transcription: bool
        :keyword enable_intermediate_results: Enables intermediate results for the transcribed speech.
        :paramtype enable_intermediate_results: bool
        """
        super().__init__(locale=locale, **kwargs)
        self.transport_type: str = "websocket"
        self.transport_url = transport_url
        self.speech_model_endpoint_id = speech_model_endpoint_id
        self.start_transcription = start_transcription
        self.enable_intermediate_results = enable_intermediate_results
